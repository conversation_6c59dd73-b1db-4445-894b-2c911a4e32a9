{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "seed": "ts-node src/scripts/seedDatabase.ts", "create-admin": "ts-node src/scripts/createAdminUser.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/cors": "^2.8.13", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^6.11.2", "helmet": "^6.2.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.15.0", "mongoose": "^8.13.2"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.13", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^18.19.86", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}