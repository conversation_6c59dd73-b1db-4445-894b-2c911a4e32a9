{"name": "taylorzhu_portfolio", "version": "1.0.0", "description": "This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.", "main": "index.js", "dependencies": {"vite": "^6.0.1", "@vitejs/plugin-react": "^4.3.4", "@react-spring/web": "^9.7.5", "@types/three": "^0.172.0", "axios": "^1.7.9", "framer-motion": "^11.15.0", "fresh": "^0.5.2", "fs-extra": "^11.2.0", "fsevents": "^2.3.3", "function-bind": "^1.1.2", "function.prototype.name": "^1.1.6", "functions-have-names": "^1.2.3", "gensync": "^1.0.0-beta.2", "get-intrinsic": "^1.2.4", "get-symbol-description": "^1.0.2", "gh-pages": "^6.2.0", "glob-parent": "^6.0.2", "globals": "^15.12.0", "globalthis": "^1.0.4", "globby": "^11.1.0", "gopd": "^1.1.0", "graceful-fs": "^4.2.11", "has-bigints": "^1.0.2", "has-flag": "^4.0.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.0.3", "has-symbols": "^1.0.3", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2", "helmet": "^8.0.0", "http-errors": "^2.0.0", "hyphenate-style-name": "^1.1.0", "iconv-lite": "^0.4.24", "ignore": "^5.3.2", "import-fresh": "^3.3.0", "imurmurhash": "^0.1.4", "inherits": "^2.0.4", "inline-style-prefixer": "^7.0.1", "internal-slot": "^1.0.7", "ipaddr.js": "^1.9.1", "is-array-buffer": "^3.0.4", "is-async-function": "^2.0.0", "is-bigint": "^1.0.4", "is-boolean-object": "^1.1.2", "is-callable": "^1.2.7", "is-core-module": "^2.15.1", "is-data-view": "^1.0.1", "is-date-object": "^1.0.5", "is-extglob": "^2.1.1", "is-finalizationregistry": "^1.1.0", "is-generator-function": "^1.0.10", "is-glob": "^4.0.3", "is-map": "^2.0.3", "is-negative-zero": "^2.0.3", "is-number": "^7.0.0", "is-number-object": "^1.0.7", "is-regex": "^1.2.0", "is-set": "^2.0.3", "is-shared-array-buffer": "^1.0.3", "is-string": "^1.0.7", "is-symbol": "^1.0.4", "is-typed-array": "^1.1.13", "is-weakmap": "^2.0.2", "is-weakref": "^1.0.2", "is-weakset": "^2.0.3", "isarray": "^2.0.5", "isexe": "^2.0.0", "iterator.prototype": "^1.1.3", "js-cookie": "^2.2.1", "js-tokens": "^4.0.0", "js-yaml": "^4.1.0", "jsesc": "^3.0.2", "json-buffer": "^3.0.1", "json-schema-traverse": "^0.4.1", "json-stable-stringify-without-jsonify": "^1.0.1", "json5": "^2.2.3", "jsonfile": "^6.1.0", "jsx-ast-utils": "^3.3.5", "keyv": "^4.5.4", "levn": "^0.4.1", "locate-path": "^6.0.0", "lodash.merge": "^4.6.2", "loose-envify": "^1.4.0", "lru-cache": "^5.1.1", "make-dir": "^3.1.0", "mdn-data": "^2.0.14", "media-typer": "^0.3.0", "merge-descriptors": "^1.0.3", "merge2": "^1.4.1", "methods": "^1.1.2", "micromatch": "^4.0.8", "mime": "^1.6.0", "mime-db": "^1.52.0", "mime-types": "^2.1.35", "minimatch": "^3.1.2", "motion-dom": "^11.14.3", "motion-utils": "^11.14.3", "ms": "^2.1.3", "nano-css": "^5.6.2", "nanoid": "^3.3.8", "natural-compare": "^1.4.0", "negotiator": "^0.6.3", "node-releases": "^2.0.18", "object-assign": "^4.1.1", "object-inspect": "^1.13.3", "object-keys": "^1.1.1", "object.assign": "^4.1.5", "object.entries": "^1.1.8", "object.fromentries": "^2.0.8", "object.values": "^1.2.0", "on-finished": "^2.4.1", "optionator": "^0.9.4", "p-limit": "^3.1.0", "p-locate": "^5.0.0", "p-try": "^2.2.0", "parent-module": "^1.0.1", "parseurl": "^1.3.3", "path-exists": "^4.0.0", "path-key": "^3.1.1", "path-parse": "^1.0.7", "path-to-regexp": "^0.1.12", "path-type": "^4.0.0", "picocolors": "^1.1.1", "picomatch": "^2.3.1", "pkg-dir": "^4.2.0", "possible-typed-array-names": "^1.0.0", "postcss": "^8.4.49", "prelude-ls": "^1.2.1", "prop-types": "^15.8.1", "proxy-addr": "^2.0.7", "punycode": "^2.3.1", "qs": "^6.13.0", "queue-microtask": "^1.2.3", "range-parser": "^1.2.1", "raw-body": "^2.5.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.5.1", "react-use": "^17.6.0", "three": "^0.172.0", "three-stdlib": "^2.35.12"}, "devDependencies": {"concurrently": "^9.1.0", "@vitejs/plugin-react": "^4.3.4", "vite": "^6.0.1", "vite-plugin-compression2": "^1.3.3", "terser": "^5.39.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "vite build", "vercel-build": "node vercel-build.js", "start": "cd server && npm install && npm start", "dev": "concurrently \"vite\" \"cd server && npm run dev\""}, "keywords": [], "author": "", "license": "ISC"}