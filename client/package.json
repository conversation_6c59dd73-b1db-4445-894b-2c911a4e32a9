{"name": "my-first-react-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"@react-spring/web": "^9.7.5", "@react-three/drei": "^9.121.3", "@react-three/fiber": "^8.17.12", "@types/three": "^0.172.0", "axios": "^1.7.9", "framer-motion": "^11.18.2", "gh-pages": "^6.2.0", "highlight.js": "^11.11.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^8.0.7", "react-router-dom": "^7.5.1", "react-use": "^17.6.0", "rehype-highlight": "^6.0.0", "rehype-katex": "^7.0.1", "rehype-mermaid": "^3.0.0", "rehype-raw": "^6.1.1", "remark-directive": "^4.0.0", "remark-gfm": "^3.0.1", "three": "^0.172.0"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/node": "^22.14.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "sharp": "^0.34.1", "terser": "^5.39.0", "vite": "^5.4.18", "vite-plugin-compression2": "^1.3.3"}}