import { motion } from 'framer-motion';
import { PageType } from '../types/navigation';
import { PERSONAL_INFO, STATS_DATA, LANDING_PAGE_ANIMATIONS } from '../types/landing';
import ResponsiveImage from './common/ResponsiveImage';
import '../styles/landing-page.css';

interface LandingPageProps {
  onOpenChat: () => void;
  onNavigate: (page: PageType) => void;
}

const LandingPage = ({ onOpenChat, onNavigate }: LandingPageProps) => {
  return (
    <motion.div
      className="landing-page"
      initial="initial"
      animate="animate"
      exit="exit"
      variants={LANDING_PAGE_ANIMATIONS.pageVariants}
      transition={{ duration: 0.5 }}
    >
      <div className="left-content">
        <header>
          <h1>
            Hello I'm<br />
            <span className="highlight" aria-label={PERSONAL_INFO.name}>{PERSONAL_INFO.name}</span>
          </h1>
          <h2>{PERSONAL_INFO.title}</h2>
          <p>{PERSONAL_INFO.description}</p>
        </header>
        <nav className="action-buttons" aria-label="Main navigation">
          <button
            type="button"
            className="primary-btn"
            onClick={() => onNavigate('about')}
            aria-label="Learn more about Taylor"
          >
            About Me
          </button>
          <button
            type="button"
            className="secondary-btn"
            onClick={() => onNavigate('contact')}
            aria-label="Get in touch with Taylor"
          >
            Contact Me
          </button>
        </nav>
      </div>

      <div className="center-content">
        <div className="image-container">
          <ResponsiveImage
            src={PERSONAL_INFO.profileImage}
            alt={`Professional headshot of ${PERSONAL_INFO.name}, Software Engineer`}
            sizes="(max-width: 768px) 100vw, 50vw"
            loading="eager"
          />
          <motion.button
            type="button"
            className="chat-bubble"
            onClick={onOpenChat}
            whileHover={{ scale: 1.05 }}
            animate={{ y: [0, -10, 0] }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            aria-label="Open chat to talk with Taylor's AI assistant"
          >
            <span role="img" aria-label="sparkles">✨</span>
            Chat with me
          </motion.button>
        </div>
      </div>

      <aside className="right-content" aria-label="Professional statistics">
        <div className="stats">
          {STATS_DATA.map((stat, index) => (
            <div key={index} className="stat-card">
              <h3 aria-label={stat.ariaLabel}>{stat.value}</h3>
              <p>{stat.label}</p>
            </div>
          ))}
        </div>
      </aside>
    </motion.div>
  );
};

export default LandingPage;
