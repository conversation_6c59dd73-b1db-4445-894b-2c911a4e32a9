/* 基础骨架屏样式 */
.skeleton {
  background: linear-gradient(90deg,
    var(--skeleton-base-color, #f0f0f0) 25%,
    var(--skeleton-highlight-color, #e0e0e0) 50%,
    var(--skeleton-base-color, #f0f0f0) 75%
  );
  background-size: 200% 100%;
  display: inline-block;
  position: relative;
  overflow: hidden;
  width: var(--skeleton-width, 100%);
  height: var(--skeleton-height, 1rem);
  border-radius: var(--skeleton-border-radius, 4px);
}

/* 深色主题下的骨架屏 */
[data-theme="dark"] .skeleton {
  --skeleton-base-color: #2a2a2a;
  --skeleton-highlight-color: #3a3a3a;
}

/* 动画效果 */
.skeleton--pulse {
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

.skeleton--wave {
  animation: skeleton-wave 1.5s linear infinite;
}

.skeleton--none {
  animation: none;
}

/* 骨架屏动画 */
@keyframes skeleton-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes skeleton-wave {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 变体样式 */
.skeleton--text {
  border-radius: 4px;
}

.skeleton--rectangular {
  border-radius: 4px;
}

.skeleton--circular {
  border-radius: 50%;
}

/* 文本容器 */
.skeleton-text-container {
  width: var(--skeleton-width, 100%);
}

.skeleton-text-container .skeleton {
  display: block;
  margin-bottom: 0.5rem;
}

.skeleton-text-container .skeleton:last-child,
.skeleton-text-container .skeleton--last-line {
  width: 60%;
  margin-bottom: 0;
}

/* LandingPage 骨架屏布局 */
.landing-page-skeleton {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  height: 100%;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 80px);
  padding: 0 4rem;
}

.left-content-skeleton {
  padding-right: 2rem;
}

.header-skeleton {
  margin-bottom: 2rem;
}

.header-skeleton .skeleton {
  margin-bottom: 1rem;
}

.buttons-skeleton {
  display: flex;
  gap: 1rem;
}

.center-content-skeleton {
  position: relative;
}

.image-skeleton {
  position: relative;
  width: 400px;
  height: 500px;
}

.chat-bubble-skeleton {
  position: absolute;
  top: 0px;
  right: -100px;
}

.right-content-skeleton {
  padding-left: 2rem;
}

.stats-skeleton {
  display: grid;
  gap: 1.5rem;
}

.stat-card-skeleton {
  background: var(--skeleton-card-bg, #fff);
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

[data-theme="dark"] .stat-card-skeleton {
  --skeleton-card-bg: #1a1a1a;
  box-shadow: 0 4px 16px rgba(0,0,0,0.3);
}

.stat-card-skeleton .skeleton:first-child {
  margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .landing-page-skeleton {
    grid-template-columns: 1fr auto;
    padding: 2rem;
  }

  .right-content-skeleton {
    display: none;
  }

  .image-skeleton {
    width: 400px;
    height: 500px;
  }
}

@media (max-width: 768px) {
  .landing-page-skeleton {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 2rem;
    text-align: center;
    place-items: center;
    padding: 2rem;
  }

  .left-content-skeleton {
    padding-right: 0;
    order: 2;
  }

  .center-content-skeleton {
    order: 1;
    margin: 0 auto;
  }

  .right-content-skeleton {
    display: block;
    order: 3;
    padding-left: 0;
    width: 100%;
  }

  .stats-skeleton {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
  }

  .stat-card-skeleton {
    text-align: center;
    padding: 1.2rem;
  }

  .buttons-skeleton {
    justify-content: center;
  }

  .chat-bubble-skeleton {
    position: static;
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .image-skeleton {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .image-skeleton {
    width: 250px;
    height: 350px;
  }

  .stats-skeleton {
    gap: 0.8rem;
  }

  .stat-card-skeleton {
    padding: 1rem;
  }
}

/* 加载状态的淡入效果 */
.skeleton-fade-in {
  animation: skeleton-fade-in 0.3s ease-in;
}

@keyframes skeleton-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
