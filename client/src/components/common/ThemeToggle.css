/* 主题切换组件样式 */
.theme-toggle {
  position: relative;
  display: inline-block;
}

.theme-toggle-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--theme-toggle-bg, rgba(255, 255, 255, 0.9));
  border: 1px solid var(--theme-toggle-border, rgba(0, 0, 0, 0.1));
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--theme-toggle-color, #333);
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  min-width: 120px;
  justify-content: space-between;
}

.theme-toggle-button:hover {
  background: var(--theme-toggle-hover-bg, rgba(255, 255, 255, 0.95));
  border-color: var(--theme-toggle-hover-border, rgba(0, 0, 0, 0.2));
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.theme-toggle-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--theme-toggle-focus, #4CAF50);
}

.theme-icon {
  font-size: 1.1rem;
}

.theme-label {
  flex: 1;
  text-align: left;
}

.dropdown-arrow {
  font-size: 0.8rem;
  color: var(--theme-toggle-arrow, #666);
  transition: transform 0.2s ease;
}

/* 背景遮罩 */
.theme-toggle-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

/* 下拉菜单 */
.theme-toggle-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  left: 0;
  right: 0;
  background: var(--theme-dropdown-bg, #fff);
  border: 1px solid var(--theme-dropdown-border, rgba(0, 0, 0, 0.1));
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--theme-option-color, #333);
  transition: all 0.2s ease;
  text-align: left;
}

.theme-option:hover {
  background: var(--theme-option-hover, rgba(76, 175, 80, 0.1));
}

.theme-option.active {
  background: var(--theme-option-active, rgba(76, 175, 80, 0.15));
  color: var(--theme-option-active-color, #4CAF50);
  font-weight: 600;
}

.theme-option-icon {
  font-size: 1.1rem;
}

.theme-option-label {
  flex: 1;
}

.theme-option-check {
  color: var(--theme-option-check, #4CAF50);
  font-weight: bold;
  font-size: 1rem;
}

/* 深色主题样式 */
[data-theme="dark"] .theme-toggle-button {
  --theme-toggle-bg: rgba(40, 40, 40, 0.9);
  --theme-toggle-border: rgba(255, 255, 255, 0.1);
  --theme-toggle-color: #fff;
  --theme-toggle-hover-bg: rgba(50, 50, 50, 0.95);
  --theme-toggle-hover-border: rgba(255, 255, 255, 0.2);
  --theme-toggle-arrow: #ccc;
}

[data-theme="dark"] .theme-toggle-dropdown {
  --theme-dropdown-bg: #2a2a2a;
  --theme-dropdown-border: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .theme-option {
  --theme-option-color: #fff;
  --theme-option-hover: rgba(76, 175, 80, 0.2);
  --theme-option-active: rgba(76, 175, 80, 0.25);
  --theme-option-active-color: #4CAF50;
  --theme-option-check: #4CAF50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .theme-toggle-button {
    min-width: 100px;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .theme-icon {
    font-size: 1rem;
  }

  .theme-toggle-dropdown {
    left: -50%;
    right: -50%;
  }

  .theme-option {
    padding: 0.6rem 0.8rem;
    font-size: 0.8rem;
  }
}

/* 动画增强 */
.theme-toggle-button {
  position: relative;
  overflow: hidden;
}

.theme-toggle-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.theme-toggle-button:hover::before {
  left: 100%;
}

[data-theme="dark"] .theme-toggle-button::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}
