import { motion } from 'framer-motion';
import { useLanguage } from '../contexts/LanguageContext';
import '../styles/creative-corner.css';

const CreativeCorner = () => {
  const { t } = useLanguage();
  
  const fadeIn = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  return (
    <div className="creative-corner">
      <motion.div 
        className="creative-header"
        initial={fadeIn.initial}
        animate={fadeIn.animate}
        transition={fadeIn.transition}
      >
        <h1>Creative <span className="highlight">Corner</span></h1>
        <p className="subtitle">Beyond the code - exploring life, books, coffee, and creativity</p>
      </motion.div>

      <div className="creative-content">
        {/* 书架区域 */}
        <motion.div 
          className="creative-section bookshelf-section"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <h2>📚 My Bookshelf</h2>
          <p className="section-intro">Books that shaped my thinking and inspired my journey</p>
          
          <div className="books-grid">
            <div className="book-item">
              <div className="book-cover">
                <img src="/book-placeholder.jpg" alt="Book cover" />
              </div>
              <div className="book-info">
                <h3>Clean Code</h3>
                <p className="author">Robert C. <PERSON></p>
                <p className="book-note">A masterpiece on writing maintainable code. Changed how I approach software development.</p>
              </div>
            </div>
            
            <div className="book-item">
              <div className="book-cover">
                <img src="/book-placeholder.jpg" alt="Book cover" />
              </div>
              <div className="book-info">
                <h3>The Pragmatic Programmer</h3>
                <p className="author">David Thomas & Andrew Hunt</p>
                <p className="book-note">Essential wisdom for every developer. Practical advice that I apply daily.</p>
              </div>
            </div>
            
            <div className="book-item">
              <div className="book-cover">
                <img src="/book-placeholder.jpg" alt="Book cover" />
              </div>
              <div className="book-info">
                <h3>Atomic Habits</h3>
                <p className="author">James Clear</p>
                <p className="book-note">Transformed my approach to personal growth and productivity.</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* 咖啡角落 */}
        <motion.div 
          className="creative-section coffee-section"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <h2>☕ Coffee Corner</h2>
          <p className="section-intro">My favorite brews and coffee experiences around the Bay Area</p>
          
          <div className="coffee-grid">
            <div className="coffee-item">
              <div className="coffee-image">
                <img src="/coffee-placeholder.jpg" alt="Coffee" />
              </div>
              <div className="coffee-info">
                <h3>Blue Bottle Coffee</h3>
                <p className="location">Oakland, CA</p>
                <p className="coffee-note">Their single-origin pour-overs are exceptional. The Hayes Valley location has the perfect coding atmosphere.</p>
              </div>
            </div>
            
            <div className="coffee-item">
              <div className="coffee-image">
                <img src="/coffee-placeholder.jpg" alt="Coffee" />
              </div>
              <div className="coffee-info">
                <h3>Ritual Coffee Roasters</h3>
                <p className="location">San Francisco, CA</p>
                <p className="coffee-note">Amazing espresso blends. Their Mission location is my go-to for weekend coding sessions.</p>
              </div>
            </div>
            
            <div className="coffee-item">
              <div className="coffee-image">
                <img src="/coffee-placeholder.jpg" alt="Coffee" />
              </div>
              <div className="coffee-info">
                <h3>Philz Coffee</h3>
                <p className="location">San Francisco, CA</p>
                <p className="coffee-note">Unique blends and personalized service. Mint Mojito is my productivity fuel.</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* 心得分享 */}
        <motion.div 
          className="creative-section thoughts-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <h2>💭 Thoughts & Reflections</h2>
          <p className="section-intro">Random thoughts, lessons learned, and insights from my journey</p>
          
          <div className="thoughts-grid">
            <div className="thought-item">
              <h3>On Work-Life Balance</h3>
              <p className="thought-date">December 2024</p>
              <p className="thought-content">
                Living in the Bay Area taught me that success isn't just about coding 24/7. 
                Some of my best solutions come during surf sessions at Ocean Beach or hiking in Marin.
              </p>
            </div>
            
            <div className="thought-item">
              <h3>The Art of Debugging</h3>
              <p className="thought-date">November 2024</p>
              <p className="thought-content">
                Debugging is like detective work. The bug is never where you think it is, 
                and the solution is often simpler than you imagine. Patience is key.
              </p>
            </div>
            
            <div className="thought-item">
              <h3>Learning in Public</h3>
              <p className="thought-date">October 2024</p>
              <p className="thought-content">
                Sharing your learning journey, even the failures, creates connections and opportunities. 
                The tech community is incredibly supportive when you're genuine about your growth.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default CreativeCorner;
