import { motion } from 'framer-motion';
import { useState, useEffect, useCallback } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import '../styles/creative-corner.css';

const CreativeCorner = () => {
  const { t } = useLanguage();
  const [playerPosition, setPlayerPosition] = useState({ x: 50, y: 70 });
  const [currentInteraction, setCurrentInteraction] = useState<string | null>(null);
  const [isNearInteractable, setIsNearInteractable] = useState<string | null>(null);
  const [currentMusic, setCurrentMusic] = useState('Lofi Hip Hop');

  const fadeIn = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  // 交互点位置
  const interactables = {
    bookshelf: { x: 20, y: 30, range: 15 },
    coffee: { x: 80, y: 40, range: 15 },
    music: { x: 50, y: 20, range: 15 }
  };

  // 检查是否靠近交互物品
  const checkNearInteractable = useCallback(() => {
    for (const [key, item] of Object.entries(interactables)) {
      const distance = Math.sqrt(
        Math.pow(playerPosition.x - item.x, 2) + Math.pow(playerPosition.y - item.y, 2)
      );
      if (distance <= item.range) {
        setIsNearInteractable(key);
        return;
      }
    }
    setIsNearInteractable(null);
  }, [playerPosition]);

  // 键盘控制
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      const speed = 3;
      let newX = playerPosition.x;
      let newY = playerPosition.y;

      switch (e.key.toLowerCase()) {
        case 'w':
        case 'arrowup':
          newY = Math.max(15, playerPosition.y - speed);
          break;
        case 's':
        case 'arrowdown':
          newY = Math.min(85, playerPosition.y + speed);
          break;
        case 'a':
        case 'arrowleft':
          newX = Math.max(5, playerPosition.x - speed);
          break;
        case 'd':
        case 'arrowright':
          newX = Math.min(95, playerPosition.x + speed);
          break;
        case 'e':
        case ' ':
          if (isNearInteractable) {
            setCurrentInteraction(isNearInteractable);
          }
          break;
        case 'escape':
          setCurrentInteraction(null);
          break;
      }

      if (newX !== playerPosition.x || newY !== playerPosition.y) {
        setPlayerPosition({ x: newX, y: newY });
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [playerPosition, isNearInteractable]);

  useEffect(() => {
    checkNearInteractable();
  }, [checkNearInteractable]);

  return (
    <div className="creative-corner">
      <motion.div
        className="creative-header"
        initial={fadeIn.initial}
        animate={fadeIn.animate}
        transition={fadeIn.transition}
      >
        <h1>Creative <span className="highlight">Corner</span></h1>
        <p className="subtitle">Explore my seaside coffee shop - Use WASD or arrow keys to move, E/Space to interact</p>
      </motion.div>

      {/* 游戏场景 */}
      <div className="game-container">
        <div className="game-scene">
          {/* 背景 */}
          <div className="scene-background">
            <div className="ocean"></div>
            <div className="beach"></div>
            <div className="coffee-shop">
              {/* 书架 */}
              <div
                className={`bookshelf ${isNearInteractable === 'bookshelf' ? 'highlight' : ''}`}
                style={{ left: `${interactables.bookshelf.x}%`, top: `${interactables.bookshelf.y}%` }}
              >
                📚
                {isNearInteractable === 'bookshelf' && (
                  <div className="interaction-hint">Press E to browse books</div>
                )}
              </div>

              {/* 咖啡吧台 */}
              <div
                className={`coffee-counter ${isNearInteractable === 'coffee' ? 'highlight' : ''}`}
                style={{ left: `${interactables.coffee.x}%`, top: `${interactables.coffee.y}%` }}
              >
                ☕
                {isNearInteractable === 'coffee' && (
                  <div className="interaction-hint">Press E to see coffee favorites</div>
                )}
              </div>

              {/* 音乐播放器 */}
              <div
                className={`music-player ${isNearInteractable === 'music' ? 'highlight' : ''}`}
                style={{ left: `${interactables.music.x}%`, top: `${interactables.music.y}%` }}
              >
                🎵
                {isNearInteractable === 'music' && (
                  <div className="interaction-hint">Press E to change music</div>
                )}
              </div>
            </div>
          </div>

          {/* 玩家角色 */}
          <div
            className="player"
            style={{
              left: `${playerPosition.x}%`,
              top: `${playerPosition.y}%`,
              transform: 'translate(-50%, -50%)'
            }}
          >
            🚶‍♂️
          </div>
        </div>

        {/* 当前音乐显示 */}
        <div className="music-display">
          🎵 Now Playing: {currentMusic}
        </div>
      </div>

      {/* 交互弹窗 */}
      {currentInteraction === 'bookshelf' && (
        <motion.div
          className="interaction-modal"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
        >
          <div className="modal-content">
            <h3>📚 My Favorite Books</h3>
            <div className="books-list">
              <div className="book-item-mini">
                <strong>Clean Code</strong> - Robert C. Martin<br/>
                <em>The foundation of writing maintainable code</em>
              </div>
              <div className="book-item-mini">
                <strong>The Pragmatic Programmer</strong> - David Thomas<br/>
                <em>Essential wisdom for every developer</em>
              </div>
              <div className="book-item-mini">
                <strong>Atomic Habits</strong> - James Clear<br/>
                <em>Small changes, remarkable results</em>
              </div>
              <div className="book-item-mini">
                <strong>Designing Data-Intensive Applications</strong> - Martin Kleppmann<br/>
                <em>The big ideas behind reliable, scalable systems</em>
              </div>
            </div>
            <button type="button" onClick={() => setCurrentInteraction(null)}>Close (ESC)</button>
          </div>
        </motion.div>
      )}

      {currentInteraction === 'coffee' && (
        <motion.div
          className="interaction-modal"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
        >
          <div className="modal-content">
            <h3>☕ Bay Area Coffee Favorites</h3>
            <div className="coffee-list">
              <div className="coffee-item-mini">
                <strong>Blue Bottle Coffee</strong> - Oakland<br/>
                <em>Perfect single-origin pour-overs, great for coding</em>
              </div>
              <div className="coffee-item-mini">
                <strong>Ritual Coffee Roasters</strong> - San Francisco<br/>
                <em>Amazing espresso blends, Mission location is my go-to</em>
              </div>
              <div className="coffee-item-mini">
                <strong>Philz Coffee</strong> - San Francisco<br/>
                <em>Mint Mojito is my productivity fuel</em>
              </div>
              <div className="coffee-item-mini">
                <strong>Sightglass Coffee</strong> - San Francisco<br/>
                <em>Industrial vibes, excellent cortados</em>
              </div>
            </div>
            <button type="button" onClick={() => setCurrentInteraction(null)}>Close (ESC)</button>
          </div>
        </motion.div>
      )}

      {currentInteraction === 'music' && (
        <motion.div
          className="interaction-modal"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
        >
          <div className="modal-content">
            <h3>🎵 Coding Playlist</h3>
            <div className="music-list">
              {['Lofi Hip Hop', 'Ambient Electronic', 'Jazz Instrumentals', 'Classical Focus', 'Synthwave'].map((music) => (
                <button
                  key={music}
                  type="button"
                  className={`music-option ${currentMusic === music ? 'active' : ''}`}
                  onClick={() => setCurrentMusic(music)}
                >
                  {music} {currentMusic === music && '🎵'}
                </button>
              ))}
            </div>
            <button type="button" onClick={() => setCurrentInteraction(null)}>Close (ESC)</button>
          </div>
        </motion.div>
      )}


    </div>
  );
};

export default CreativeCorner;
