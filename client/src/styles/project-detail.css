/* client/src/styles/project-detail.css */
.project-detail-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
  }
  
  .back-button {
    display: inline-flex;
    align-items: center;
    background: none;
    border: none;
    color: #4CAF50;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: background 0.3s ease;
    cursor: pointer;
  }
  
  .back-button:hover {
    background: rgba(76, 175, 80, 0.1);
  }
  
  .project-detail-header {
    margin-bottom: 2rem;
  }
  
  .project-detail-header h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
  }
  
  .category-badge {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 4px;
    font-size: 0.9rem;
    color: white;
    text-transform: capitalize;
  }
  
  .category-badge.frontend {
    background: #2196F3;
  }
  
  .category-badge.backend {
    background: #9C27B0;
  }
  
  .category-badge.fullstack {
    background: #4CAF50;
  }
  
  .category-badge.research {
    background: #FF9800;
  }
  
  .project-carousel {
    position: relative;
    margin-bottom: 2rem;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
  
  .carousel-image {
    width: 100%;
    height: auto;
    max-height: 500px;
    object-fit: cover;
  }
  
  .carousel-controls {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
  }
  
  .carousel-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #e0e0e0;
    border: none;
    padding: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  
  .carousel-dot.active {
    background: #4CAF50;
  }
  
  .project-detail-description {
    margin-bottom: 2rem;
  }
  
  .long-description {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: #333;
  }
  
  .project-tech-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }
  
  .tech-tag {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
    padding: 0.3rem 0.8rem;
    border-radius: 16px;
    font-size: 0.9rem;
  }
  
  .project-details-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .detail-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }
  
  .detail-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #4CAF50;
  }
  
  .detail-card p {
    line-height: 1.6;
    color: #555;
  }
  
  .project-links {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
  }
  
  .github-link, .demo-link {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
  }
  
  .github-link {
    background: #333;
    color: white;
  }
  
  .github-link:hover {
    background: #555;
    transform: translateY(-2px);
  }
  
  .demo-link {
    background: #4CAF50;
    color: white;
  }
  
  .demo-link:hover {
    background: #45a049;
    transform: translateY(-2px);
  }
  
  /* 响应式调整 */
  @media (max-width: 768px) {
    .project-detail-container {
      padding: 1rem;
    }
    
    .project-detail-header h1 {
      font-size: 2rem;
    }
    
    .project-details-section {
      grid-template-columns: 1fr;
    }
  }
  
  @media (max-width: 480px) {
    .project-detail-header h1 {
      font-size: 1.8rem;
    }
    
    .project-links {
      flex-direction: column;
    }
    
    .github-link, .demo-link {
      width: 100%;
      text-align: center;
    }
  }
