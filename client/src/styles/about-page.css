.about-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    color: #333;
  }
  
  /* Header styles */
  .about-header {
    text-align: center;
    margin-bottom: 4rem;
  }
  
  .about-header h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
  }
  
  .highlight {
    color: #4CAF50;
  }
  
  .subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
  }

  /* 内联导航样式 */
  .about-nav {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
  }

  .about-nav .nav-items {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
    gap: 1rem;
    background: var(--bg-secondary, rgba(248, 249, 250, 0.8));
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    padding: 1rem 1.5rem;
    border-radius: 50px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-primary, rgba(255, 255, 255, 0.2));
    max-width: 600px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .about-nav .nav-items::-webkit-scrollbar {
    display: none;
  }

  .about-nav .nav-item {
    background: transparent;
    border: none;
    padding: 0.6rem 1.2rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary, #666);
    text-align: center;
    white-space: nowrap;
    min-width: fit-content;
  }

  .about-nav .nav-item:hover {
    background: var(--nav-hover, rgba(76, 175, 80, 0.1));
    color: var(--primary-color, #4CAF50);
    transform: translateY(-2px);
  }

  .about-nav .nav-item.active {
    background: var(--primary-color, #4CAF50);
    color: white;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  }

  .about-nav .nav-item.active:hover {
    transform: translateY(-2px) scale(1.02);
  }

  /* 深色主题支持 */
  [data-theme="dark"] .about-page {
    color: var(--text-primary, #fff);
  }

  [data-theme="dark"] .about-nav .nav-items {
    background: var(--bg-secondary, rgba(30, 30, 30, 0.8));
    border: 1px solid var(--border-primary, rgba(255, 255, 255, 0.1));
  }

  [data-theme="dark"] .about-nav .nav-item {
    color: var(--text-secondary, #ccc);
  }

  [data-theme="dark"] .about-nav .nav-item:hover {
    background: var(--nav-hover, rgba(76, 175, 80, 0.2));
  }

  [data-theme="dark"] .about-section {
    background: var(--card-bg, #1e1e1e);
    color: var(--text-primary, #fff);
  }

  /* 深色主题支持 */
  [data-theme="dark"] .about-page {
    color: var(--text-primary, #fff);
  }

  [data-theme="dark"] .about-nav .nav-items {
    background: var(--bg-secondary, rgba(30, 30, 30, 0.8));
    border: 1px solid var(--border-primary, rgba(255, 255, 255, 0.1));
  }

  [data-theme="dark"] .about-nav .nav-item {
    color: var(--text-secondary, #ccc);
  }

  [data-theme="dark"] .about-nav .nav-item:hover {
    background: var(--nav-hover, rgba(76, 175, 80, 0.2));
  }

  [data-theme="dark"] .about-section {
    background: var(--card-bg, #1e1e1e);
    color: var(--text-primary, #fff);
  }
  
  /* Content layout */
  .about-content {
    display: grid;
    gap: 4rem;
  }
  
  .about-section {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
  }
  
  .about-section h2 {
    color: #4CAF50;
    margin-bottom: 2rem;
    font-size: 1.8rem;
  }
  
  /* Timeline styles */
  .timeline {
    position: relative;
    padding-left: 2rem;
    max-width: 800px;
    margin: 0 auto;
  }
  
  .timeline-item {
    position: relative;
    padding-bottom: 2.5rem;
    padding-left: 2rem;
    padding-right: 120px;
    border-left: 2px solid #4CAF50;
  }
  
  .timeline-item:last-child {
    padding-bottom: 0;
  }
  
  .timeline-date {
    position: absolute;
    left: -8rem;
    color: #4CAF50;
    font-weight: 600;
  }
  
  .timeline-content {
    position: relative;
  }
  
  .timeline-content h3 {
    margin-bottom: 0.5rem;
    color: #333;
    font-size: 1.2rem;
  }
  
  .timeline-content p {
    color: #666;
    margin-bottom: 1rem;
  }
  
  .timeline-content ul {
    list-style: none;
    padding-left: 0;
    margin-top: 1rem;
  }
  
  .timeline-content ul li {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
  }
  
  .timeline-content ul li::before {
    content: "•";
    color: #4CAF50;
    position: absolute;
    left: 0;
  }
  
  /* Company logo styles */
  .company-logo {
    position: absolute;
    right: -100px;
    top: 50%;
    transform: translateY(-50%);
    width: 80px;
    height: 80px;
    border-radius: 8px;
    padding: 8px;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }
  
  .company-logo:hover {
    transform: translateY(-50%) scale(1.05);
  }
  
  .company-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  
  /* Skills section */
  .skills-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
  
  .skill-category h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
  }
  
  .skill-category ul {
    list-style: none;
    padding-left: 0;
  }
  
  .skill-category ul li {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
  }
  
  .skill-category ul li::before {
    content: "→";
    color: #4CAF50;
    position: absolute;
    left: 0;
  }
  
  /* Project card styles */
  .project-card {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
  }
  
  .project-card h3 {
    color: #333;
    margin-bottom: 0.5rem;
  }
  
  .project-duration {
    color: #4CAF50;
    font-weight: 600;
    margin-bottom: 1rem;
  }
  
  .project-card ul {
    list-style: none;
    padding-left: 0;
  }
  
  .project-card ul li {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
  }
  
  .project-card ul li::before {
    content: "→";
    color: #4CAF50;
    position: absolute;
    left: 0;
  }
  
  /* Interests section */
  .interests-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }
  
  .interest-item {
    text-align: center;
    padding: 1.5rem;
    border-radius: 0.5rem;
    background: rgba(76, 175, 80, 0.05);
    transition: transform 0.3s ease;
  }
  
  .interest-item:hover {
    transform: translateY(-5px);
  }
  
  .interest-item h3 {
    color: #333;
    margin-bottom: 0.5rem;
  }
  
  .interest-item p {
    color: #666;
  }
  
  /* Contact section */
  .contact-section {
    text-align: center;
  }
  
  .contact-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1.5rem;
  }
  
  .contact-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: rgba(76, 175, 80, 0.1);
    border-radius: 2rem;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
  }
  
  .contact-link:hover {
    background: rgba(76, 175, 80, 0.2);
    transform: translateY(-2px);
  }
  
  .contact-link span {
    font-size: 1.2rem;
  }
  
  /* Responsive design */
  @media (max-width: 1024px) {
    .about-page {
      padding: 1.5rem;
    }
  
    .timeline {
      padding-left: 1rem;
    }
  
    .timeline-date {
      left: -6rem;
    }
  }
  
  @media (max-width: 768px) {
    .about-page {
      padding: 1rem;
    }

    .about-header h1 {
      font-size: 2.5rem;
    }

    .about-nav .nav-items {
      padding: 0.8rem 1rem;
      gap: 0.5rem;
      max-width: 100%;
      flex-wrap: nowrap;
      overflow-x: auto;
    }

    .about-nav .nav-item {
      padding: 0.5rem 0.8rem;
      font-size: 0.85rem;
      flex-shrink: 0;
      min-width: 80px;
    }
  
    .skills-grid {
      grid-template-columns: 1fr;
      gap: 2rem;
    }
  
    .timeline-item {
      padding-right: 0;
    }
  
    .timeline-date {
      position: relative;
      left: 0;
      margin-bottom: 0.5rem;
    }
  
    .company-logo {
      position: relative;
      right: auto;
      top: auto;
      transform: none;
      margin: 1rem 0;
      width: 60px;
      height: 60px;
    }
  
    .company-logo:hover {
      transform: scale(1.05);
    }
  
    .contact-info {
      flex-direction: column;
      gap: 1rem;
      padding: 0 1rem;
    }
  
    .contact-link {
      justify-content: center;
    }
  }
  
  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Print styles */
  @media print {
    .about-page {
      padding: 0;
    }
  
    .about-section {
      box-shadow: none;
      page-break-inside: avoid;
    }
  
    .company-logo {
      print-color-adjust: exact;
      -webkit-print-color-adjust: exact;
    }
  }
