/* 博客页面主样式 */
.blog-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* 博客页头 */
.blog-header {
  text-align: center;
  margin-bottom: 3rem;
}

.blog-header h1 {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.blog-header .subtitle {
  font-size: 1.1rem;
  color: #666;
}

.highlight {
  color: #4CAF50;
}

/* 过滤导航 */
.filter-nav {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.6rem 1.2rem;
  border: 1px solid #e0e0e0;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.filter-btn:hover {
  border-color: #4CAF50;
  color: #4CAF50;
}

.filter-btn.active {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

/* 博客卡片网格 */
.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
}

/* 博客卡片 */
.blog-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  cursor: pointer;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
}

.blog-card-image {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.blog-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.blog-card:hover .blog-card-image img {
  transform: scale(1.05);
}

.collection-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #4CAF50;
  color: white;
  font-size: 0.7rem;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
}

.blog-card-content {
  padding: 1.5rem;
}

.blog-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.8rem;
  font-size: 0.8rem;
  color: #666;
}

.blog-title {
  font-size: 1.3rem;
  margin-bottom: 0.8rem;
  line-height: 1.4;
  color: #333;
}

.blog-excerpt {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1.2rem;
  line-height: 1.6;
}

.blog-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.blog-tag {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
  font-size: 0.8rem;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
}

/* 博客文章页 */
.blog-post-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.blog-post-header {
  margin-bottom: 2rem;
}

.blog-post-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.blog-post-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1rem;
}

.blog-post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.post-tag {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
  font-size: 0.8rem;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
}

.blog-post-cover {
  margin-bottom: 2rem;
}

.blog-post-cover img {
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.blog-post-content {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #333;
}

/* Markdown 标题样式 */
.blog-post-content h1,
.blog-post-content h2,
.blog-post-content h3,
.blog-post-content h4,
.blog-post-content h5,
.blog-post-content h6 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.3;
}

.blog-post-content h1 {
  font-size: 2.2rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.blog-post-content h2 {
  font-size: 1.8rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.blog-post-content h3 {
  font-size: 1.5rem;
}

.blog-post-content h4 {
  font-size: 1.3rem;
}

.blog-post-content h5 {
  font-size: 1.1rem;
}

.blog-post-content h6 {
  font-size: 1rem;
  color: #666;
}

/* 段落和文本样式 */
.blog-post-content p {
  margin-bottom: 1.5rem;
}

.blog-post-content strong {
  font-weight: 600;
}

.blog-post-content em {
  font-style: italic;
}

.blog-post-content del {
  text-decoration: line-through;
}

/* 列表样式 */
.blog-post-content ul,
.blog-post-content ol {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

.blog-post-content li {
  margin-bottom: 0.5rem;
}

.blog-post-content ul li {
  list-style-type: disc;
}

.blog-post-content ol li {
  list-style-type: decimal;
}

/* 代码样式 */
.blog-post-content code {
  font-family: monospace;
  background: #f5f5f5;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-size: 0.9em;
  color: #d63384;
}

.blog-post-content pre {
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 5px;
  overflow-x: auto;
  margin-bottom: 1.5rem;
}

.blog-post-content pre code {
  background: none;
  padding: 0;
  font-size: 0.9rem;
  color: #333;
  line-height: 1.6;
}

/* 引用样式 */
.blog-post-content blockquote {
  border-left: 4px solid #4CAF50;
  padding: 0.5rem 0 0.5rem 1rem;
  color: #666;
  font-style: italic;
  margin: 1.5rem 0;
  background: rgba(76, 175, 80, 0.05);
}

.blog-post-content blockquote p {
  margin-bottom: 0.5rem;
}

.blog-post-content blockquote p:last-child {
  margin-bottom: 0;
}

/* 链接样式 */
.blog-post-content a {
  color: #4CAF50;
  text-decoration: none;
  border-bottom: 1px dotted #4CAF50;
  transition: all 0.2s ease;
}

.blog-post-content a:hover {
  color: #3d8b40;
  border-bottom: 1px solid #3d8b40;
}

/* 表格样式 */
.blog-post-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1.5rem;
  overflow-x: auto;
  display: block;
}

.blog-post-content table th,
.blog-post-content table td {
  border: 1px solid #ddd;
  padding: 0.5rem;
  text-align: left;
}

.blog-post-content table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.blog-post-content table tr:nth-child(even) {
  background-color: #f9f9f9;
}

/* 水平线 */
.blog-post-content hr {
  height: 1px;
  background-color: #eee;
  border: none;
  margin: 2rem 0;
}

/* 图片样式 */
.blog-post-content img {
  max-width: 100%;
  height: auto;
  border-radius: 5px;
  margin: 1.5rem 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.collection-navigation {
  margin-top: 3rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
  text-align: center;
}

.view-collection-btn {
  display: inline-block;
  background: #4CAF50;
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 24px;
  text-decoration: none;
  margin-top: 1rem;
  transition: background 0.3s ease;
  cursor: pointer;
  border: none;
}

.view-collection-btn:hover {
  background: #3d8b40;
}

/* 无文章提示 */
.no-posts {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  background: rgba(255,255,255,0.5);
  border-radius: 12px;
  color: #666;
}

/* 返回按钮 */
.back-button {
  display: inline-flex;
  align-items: center;
  background: none;
  border: none;
  color: #4CAF50;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  transition: background 0.3s ease;
  cursor: pointer;
}

.back-button:hover {
  background: rgba(76, 175, 80, 0.1);
}

/* 合集相关样式 */
.collection-view {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.collection-header {
  margin-bottom: 3rem;
  text-align: center;
}

.collection-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.collection-description {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 1.5rem;
}

.collection-meta {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1.5rem;
}

.collection-author {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.collection-author img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.collection-info {
  display: flex;
  gap: 1rem;
  color: #666;
}

.collection-chapters {
  margin-top: 2rem;
}

.collection-chapter {
  margin-bottom: 3rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 2rem;
}

.chapter-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.chapter-description {
  font-size: 1rem;
  color: #666;
  margin-bottom: 1.5rem;
}

.chapter-articles {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.chapter-article {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  cursor: pointer;
}

.chapter-article:hover {
  background: rgba(76, 175, 80, 0.05);
}

.article-order {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #4CAF50;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.article-info {
  flex: 1;
}

.article-info h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.article-info p {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.article-meta {
  font-size: 0.8rem;
  color: #999;
}

/* 合集列表样式 */
.collections-container {
  margin-top: 4rem;
}

.collections-title {
  font-size: 2rem;
  text-align: center;
  margin-bottom: 0.5rem;
}

.collections-subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 2rem;
}

.collections-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
}

.collection-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  height: 100%;
}

.collection-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
}

.collection-image {
  position: relative;
  height: 160px;
}

.collection-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.collection-overlay {
  position: absolute;
  inset: 0;
  background: rgba(76, 175, 80, 0.7);
  transition: opacity 0.3s ease;
}

.collection-content {
  padding: 1.5rem;
}

.collection-content h3 {
  font-size: 1.3rem;
  margin-bottom: 0.8rem;
}

.collection-content p {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.collection-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 0.8rem;
  color: #666;
}

.collection-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.collection-tag {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
  font-size: 0.8rem;
  padding: 0.25rem 0.6rem;
  border-radius: 12px;
}

/* 加载和错误状态 */
.loading, .not-found {
  text-align: center;
  padding: 3rem;
  margin: 2rem auto;
  max-width: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.loading {
  font-size: 1.2rem;
  color: #666;
}

.not-found h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .blog-grid {
    grid-template-columns: 1fr;
  }

  .blog-post-container, .collection-view {
    padding: 1.5rem;
  }

  .blog-post-header h1, .collection-header h1 {
    font-size: 2rem;
  }

  .blog-post-content {
    font-size: 1rem;
  }

  .collections-grid {
    grid-template-columns: 1fr;
  }

  .collection-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
}
