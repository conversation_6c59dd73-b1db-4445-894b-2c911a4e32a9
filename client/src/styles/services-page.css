.services-page {
    position: top;
    min-height: 100vh;
    background: linear-gradient(to bottom, #0a0a0a, #1a1a1a);
    color: #fff;
    overflow: hidden;
  }
  
  .services-title {
    position: absolute;
    top: 80px;
    left: 0;
    right: 0;
    text-align: center;
    z-index: 10;
    pointer-events: none;
  }
  
  .services-title h1 {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #4CAF50, #2196F3);
    -webkit-background-clip: text; /* WebKit 前缀属性 */
    -webkit-text-fill-color: transparent;
  }
  
  .services-title p {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
  }
  
  .scene-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    z-index: 1;
  }
  
  .services-info {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 120px 2rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
  }
  
  .service-card {
    background: rgba(2, 25, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
  }
  
  .service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .service-card p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }
  
  .service-card ul {
    list-style: none;
    padding: 0;
  }
  
  .service-card ul li {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
    color: rgba(255, 255, 255, 0.8);
  }
  
  .service-card ul li::before {
    content: '▹';
    position: absolute;
    left: 0;
    color: #4CAF50;
  }
  
  @media (max-width: 768px) {
    .services-title h1 {
      font-size: 2.5rem;
    }
  
    .services-info {
      grid-template-columns: 1fr;
      padding: 100px 1rem 1rem;
    }
  
    .service-card {
      padding: 1.5rem;
    }
  }
  
  /* Animation classes */
  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
    100% {
      transform: translateY(0px);
    }
  }
/* Add to existing services-page.css */

.demo-card {
  cursor: pointer;
  transition: transform 0.3s ease;
}

.demo-button {
  margin-top: 1rem;
  padding: 0.8rem 1.5rem;
  background: #4CAF50;
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.demo-button:hover {
  background: #45a049;
  transform: translateY(-2px);
}

.demo-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.demo-modal-content {
  position: relative;
  width: 90%;
  height: 90%;
  background: #1a1a1a;
  border-radius: 16px;
  overflow: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 1001;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}
