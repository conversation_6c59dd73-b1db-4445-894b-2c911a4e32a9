/* src/styles/projects-page.css */
.projects-section {
  padding: 4rem 2rem;
  background-color: #fff;
}

.projects-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 标题样式 */
.section-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 2rem;
  text-align: center;
}

.highlight {
  color: #4CAF50;
}

/* 过滤导航 */
.filter-nav {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
}

.filter-btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 24px;
  background: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  border-color: #4CAF50;
  color: #4CAF50;
}

.filter-btn.active {
  background-color: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

/* 项目网格 */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

/* 项目卡片 */
.project-card {
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.project-preview {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease, opacity 0.3s ease, filter 0.3s ease;
}

.preview-image.loading {
  opacity: 0.5;
  filter: blur(5px);
}

.preview-image.loaded {
  opacity: 1;
  filter: blur(0);
}

.project-card:hover .preview-image {
  transform: scale(1.05);
}

.preview-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card:hover .preview-overlay {
  opacity: 1;
}

.preview-btn {
  padding: 0.75rem 1.5rem;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 24px;
  font-weight: 500;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.3s ease;
}

.project-card:hover .preview-btn {
  transform: translateY(0);
  opacity: 1;
}

.project-info {
  padding: 1.5rem;
}

.project-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.project-description {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tech-tag {
  padding: 0.25rem 0.75rem;
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
  border-radius: 12px;
  font-size: 0.875rem;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  font-size: 1.5rem;
  color: #333;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0.5rem;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 2rem;
}

.project-carousel {
  position: relative;
  margin-bottom: 2rem;
}

.carousel-image {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.carousel-controls {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.carousel-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #e0e0e0;
  border: none;
  padding: 0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.carousel-dot.active {
  background: #4CAF50;
}

.project-details section {
  margin-bottom: 1.5rem;
}

.project-details h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: #4CAF50;
}

.project-details p {
  color: #666;
  line-height: 1.6;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid #e0e0e0;
  text-align: center;
}

.view-project-btn {
  display: inline-block;
  padding: 0.75rem 2rem;
  background: #4CAF50;
  color: white;
  text-decoration: none;
  border-radius: 24px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.view-project-btn:hover {
  background: #45a049;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .projects-section {
    padding: 2rem 1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .filter-nav {
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
    -webkit-overflow-scrolling: touch;
  }

  .filter-btn {
    white-space: nowrap;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .modal-overlay {
    padding: 1rem;
  }

  .modal-content {
    max-height: 85vh;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease forwards;
}
