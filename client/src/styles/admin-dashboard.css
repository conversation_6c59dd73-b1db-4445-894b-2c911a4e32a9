/* client/src/styles/admin-dashboard.css */
.admin-dashboard-container {
    display: flex;
    height: 100vh;
    background: #f5f5f5;
  }
  
  /* 侧边栏样式 */
  .admin-sidebar {
    width: 240px;
    background: #2c3e50;
    color: white;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .admin-sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .admin-sidebar-header h1 {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    color: white;
  }
  
  .admin-user-info {
    font-size: 0.9rem;
    color: #b8c7ce;
  }
  
  .admin-navigation {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 1rem 0;
  }
  
  .admin-nav-item {
    padding: 0.8rem 1.5rem;
    text-align: left;
    background: none;
    border: none;
    color: #b8c7ce;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.95rem;
  }
  
  .admin-nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }
  
  .admin-nav-item.active {
    background: #4CAF50;
    color: white;
  }
  
  .admin-sidebar-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .admin-exit-button,
  .admin-logout-button {
    padding: 0.6rem 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
  }
  
  .admin-exit-button {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }
  
  .admin-exit-button:hover {
    background: rgba(255, 255, 255, 0.2);
  }
  
  .admin-logout-button {
    background: #e53935;
    color: white;
  }
  
  .admin-logout-button:hover {
    background: #d32f2f;
  }
  
  /* 内容区域样式 */
  .admin-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
  }
  
  /* 仪表板概览样式 */
  .admin-dashboard-overview h2,
  .admin-section-placeholder h2 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: #333;
  }
  
  .admin-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .admin-stat-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .admin-stat-card h3 {
    font-size: 1rem;
    color: #666;
    margin-bottom: 0.5rem;
  }
  
  .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #4CAF50;
  }
  
  .admin-recent-activity {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .admin-recent-activity h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #333;
  }
  
  /* 占位内容样式 */
  .admin-section-placeholder {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
  }
  
  .admin-section-placeholder p {
    color: #666;
    margin-bottom: 1rem;
  }
  
  /* 响应式样式 */
  @media (max-width: 768px) {
    .admin-dashboard-container {
      flex-direction: column;
    }
    
    .admin-sidebar {
      width: 100%;
      height: auto;
    }
    
    .admin-content {
      padding: 1rem;
    }
    
    .admin-stats-grid {
      grid-template-columns: 1fr;
    }
  }

  /* 添加到 client/src/styles/admin-dashboard.css */

/* 设置面板样式 */
.admin-settings {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.admin-settings h2 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.admin-profile-section {
  padding: 1.5rem;
  background: #f9f9f9;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.admin-profile-info {
  margin-bottom: 2rem;
}

.profile-item {
  display: flex;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.profile-item:last-child {
  border-bottom: none;
}

.profile-item label {
  width: 120px;
  font-weight: 600;
  color: #555;
}

.profile-item span {
  flex: 1;
  color: #333;
}

.admin-action-buttons {
  display: flex;
  gap: 1rem;
}

.admin-button {
  padding: 0.7rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.admin-button.secondary {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.admin-button.secondary:hover {
  background: #e0e0e0;
}

.admin-button.danger {
  background: #f44336;
  color: white;
}

.admin-button.danger:hover {
  background: #d32f2f;
}
