.contact-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
  }
  
  .contact-page h1 {
    text-align: center;
    margin-bottom: 40px;
    color: #4CAF50;
    font-size: 2.5rem;
    font-weight: 700;
  }
  
  /* 社交链接样式 */
  .social-links {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-bottom: 50px;
  }
  
  .social-button {
    background: white;
    padding: 12px 24px;
    border-radius: 12px;
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border: 1px solid #eee;
  }
  
  .social-button-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .social-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }
  
  /* 表单容器样式 */
  .contact-form-container {
    background: white;
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.1);
  }
  
  .contact-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  
  .form-row {
    display: flex;
    gap: 24px;
  }
  
  .form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .form-group label {
    font-weight: 500;
    color: #333;
  }
  
  .form-group input,
  .form-group textarea {
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s ease;
    background-color: white;
  }
  
  .form-group input:focus,
  .form-group textarea:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
  }
  
  .form-group input:disabled,
  .form-group textarea:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
  
  /* 提交状态样式 */
  .submit-status {
    padding: 16px;
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
  }
  
  .submit-status.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }
  
  .submit-status.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
  
  /* 提交按钮样式 */
  .submit-button {
    background-color: #4CAF50;
    color: white;
    padding: 14px 28px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }
  
  .submit-button:hover:not(:disabled) {
    background-color: #45a049;
    transform: translateY(-2px);
  }
  
  .submit-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
  
  /* 加载动画 */
  .loading-spinner {
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  
  /* 社交按钮悬浮效果 */
  .social-button:hover {
    box-shadow: 0 6px 16px rgba(0,0,0,0.15);
    transform: translateY(-2px);
  }
  
  /* 响应式设计 */
  @media (max-width: 768px) {
    .contact-page {
      padding: 20px;
    }
  
    .contact-page h1 {
      font-size: 2rem;
    }
  
    .form-row {
      flex-direction: column;
      gap: 16px;
    }
  
    .social-links {
      flex-direction: column;
      align-items: center;
      gap: 16px;
    }
  
    .social-button {
      width: 200px;
      justify-content: center;
    }
  
    .contact-form-container {
      padding: 20px;
    }
  
    .submit-button {
      width: 100%;
    }
  }
  
  @media (max-width: 480px) {
    .contact-page h1 {
      font-size: 1.8rem;
    }
  
    .social-button {
      width: 100%;
      max-width: 280px;
    }
  }
  
  /* 占位符文本颜色 */
  .form-group input::placeholder,
  .form-group textarea::placeholder {
    color: #999;
  }
