.chat-window {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  position: relative;
  height: 100%;
  scroll-behavior: smooth;
}

.message-footer {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  margin-top: 4px;
}

.message {
  max-width: 75%;
  animation: fadeIn 0.3s ease;
  position: relative;
  font-size: 14px;
  margin-bottom: 12px;
  transition: transform 0.2s ease;
}

.message.visible {
  transform: translateY(0);
}

.message.hidden {
  transform: translateY(20px);
}

.message-user {
  margin-left: auto;
  background-color: #0084ff;
  color: white;
  border-radius: 18px 18px 4px 18px;
  padding: 12px 16px;
}

.message-ai {
  margin-right: auto;
  background-color: #f0f2f5;
  color: #000;
  border-radius: 18px 18px 18px 4px;
  padding: 12px 16px;
}

.message-user, .message-ai {
  padding: 8px 12px;
  border-radius: 14px 14px 4px 14px;
}

.message-sending {
  opacity: 0.7;
}

.message-error {
  opacity: 0.8;
}

.message-time {
  font-size: 12px;
}

.message-user .message-time {
  color: rgba(255, 255, 255, 0.8);
}

.message-ai .message-time {
  color: #666;
}

.message-input {
  padding: 12px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 10px;
  background: #fff;
  border-radius: 0 0 12px 12px;
  position: sticky;
  bottom: 0;
  z-index: 2;
}

/* 新增的自动调整大小输入框样式 */
.auto-resize-input {
  flex: 1;
  min-height: 24px;
  max-height: 150px;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 24px;
  font-size: 16px;
  line-height: 1.5;
  resize: none;
  font-family: inherit;
  transition: all 0.2s ease;
  -webkit-appearance: none;
  overflow-y: auto;
}

.auto-resize-input:focus {
  outline: none;
  border-color: #0084ff;
  box-shadow: 0 0 0 2px rgba(0, 132, 255, 0.2);
}

.message-input button {
  padding: 12px 24px;
  min-width: 80px;
  background-color: #0084ff;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;
  align-self: flex-end;
}

.message-input button:hover:not(:disabled) {
  background-color: #0073e6;
}

.message-input button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.retry-button {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #666;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
}

.sending-indicator {
  color: #666;
  display: flex;
  align-items: center;
}

.sending-indicator .material-icons {
  font-size: 14px;
  animation: rotate 2s linear infinite;
}

.typing {
  position: relative;
}

.typing-cursor {
  display: inline-block;
  width: 3px;
  height: 15px;
  background: currentColor;
  margin-left: 4px;
  vertical-align: middle;
  animation: blink 1s infinite;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background: #f0f2f5;
  border-radius: 18px;
  width: fit-content;
  margin-bottom: 8px;
}

.loading-indicator {
  position: absolute;
  bottom: 70px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: fadeIn 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.loading-indicator::before {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: rotate 1s linear infinite;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: #90949c;
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out;
  display: inline-block;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

.clear-history-button {
  position: sticky;
  top: 0;
  float: right;
  margin: 0 0 10px 10px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(245, 245, 245, 0.9);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  opacity: 0.6;
}

.clear-history-button:hover {
  opacity: 1;
  background-color: #dc3545;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.clear-history-button:hover .material-icons {
  color: white;
}

.clear-history-button .material-icons {
  font-size: 16px;
  color: #666;
  transition: color 0.2s ease;
}

.clear-history-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .chat-window {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .message-list {
    flex: 1;
    height: calc(100vh - 100px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .message-input {
    position: sticky;
    bottom: 0;
    padding: 8px;
    position: -webkit-sticky;
    background: white;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
  }

  .message {
    max-width: 85%;
  }

  .auto-resize-input {
    font-size: 16px; /* 防止iOS自动缩放 */
  }
}

/* iOS Safari 特殊处理 */
@supports (-webkit-touch-callout: none) {
  .chat-window {
    height: -webkit-fill-available;
  }
}
