/* client/src/styles/admin-login.css */
.admin-login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 60px);
    padding: 2rem;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }
  
  .admin-login-card {
    width: 100%;
    max-width: 480px;
    padding: 2.5rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
  
  .admin-login-header {
    text-align: center;
    margin-bottom: 2rem;
  }
  
  .admin-login-header h1 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 0.5rem;
  }
  
  .admin-login-header p {
    color: #666;
    font-size: 1rem;
  }
  
  .admin-login-error {
    background-color: #ffebee;
    color: #d32f2f;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
  }
  
  .admin-login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .form-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
  }
  
  .form-group input {
    padding: 0.8rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
  }
  
  .form-group input:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  }
  
  .form-group input:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
  
  .password-input-container {
    position: relative;
  }
  
  .toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 0.8rem;
    padding: 0;
  }
  
  .admin-login-button {
    padding: 0.8rem 1.5rem;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }
  
  .admin-login-button:hover:not(:disabled) {
    background-color: #43a047;
  }
  
  .admin-login-button:disabled {
    background-color: #a5d6a7;
    cursor: not-allowed;
  }
  
  .loading-spinner {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  
  .admin-login-footer {
    margin-top: 2rem;
    text-align: center;
    color: #757575;
    font-size: 0.85rem;
  }
  
  /* 响应式样式 */
  @media (max-width: 600px) {
    .admin-login-card {
      padding: 1.5rem;
    }
    
    .admin-login-header h1 {
      font-size: 1.8rem;
    }
  }
