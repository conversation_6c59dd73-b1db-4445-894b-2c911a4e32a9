.creative-corner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  color: #333;
  min-height: 100vh;
}

/* 游戏容器 */
.game-container {
  margin: 2rem 0;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.game-scene {
  position: relative;
  width: 100%;
  height: 500px;
  overflow: hidden;
  cursor: crosshair;
}

/* 场景背景 */
.scene-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom,
    #87CEEB 0%,     /* 天空蓝 */
    #87CEEB 40%,    /* 天空蓝 */
    #F0E68C 40%,    /* 沙滩黄 */
    #F0E68C 70%,    /* 沙滩黄 */
    #8B4513 70%,    /* 咖啡店棕 */
    #8B4513 100%    /* 咖啡店棕 */
  );
}

.ocean {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 40%;
  background: linear-gradient(45deg, #4682B4, #87CEEB);
  animation: wave 3s ease-in-out infinite;
}

@keyframes wave {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.beach {
  position: absolute;
  top: 40%;
  left: 0;
  width: 100%;
  height: 30%;
  background: linear-gradient(45deg, #F4A460, #F0E68C);
}

.coffee-shop {
  position: absolute;
  top: 70%;
  left: 0;
  width: 100%;
  height: 30%;
  background: linear-gradient(45deg, #8B4513, #A0522D);
}

/* 游戏元素 */
.player {
  position: absolute;
  font-size: 2rem;
  z-index: 10;
  transition: all 0.2s ease;
  filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));
}

.bookshelf, .coffee-counter, .music-player {
  position: absolute;
  font-size: 3rem;
  z-index: 5;
  transition: all 0.3s ease;
  cursor: pointer;
  filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));
}

.bookshelf.highlight, .coffee-counter.highlight, .music-player.highlight {
  transform: scale(1.2);
  filter: drop-shadow(0 0 10px #4CAF50);
}

.interaction-hint {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  white-space: nowrap;
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateX(-50%) translateY(0); }
  50% { transform: translateX(-50%) translateY(-5px); }
}

.music-display {
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 1rem;
  text-align: center;
  font-size: 1.1rem;
}

/* 交互弹窗 */
.interaction-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.modal-content h3 {
  color: #4CAF50;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.books-list, .coffee-list {
  display: grid;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.book-item-mini, .coffee-item-mini {
  padding: 1rem;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 0.5rem;
  border-left: 4px solid #4CAF50;
}

.book-item-mini strong, .coffee-item-mini strong {
  color: #333;
  display: block;
  margin-bottom: 0.5rem;
}

.book-item-mini em, .coffee-item-mini em {
  color: #666;
  font-style: italic;
}

.music-list {
  display: grid;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.music-option {
  padding: 0.8rem 1.5rem;
  background: rgba(76, 175, 80, 0.1);
  border: 2px solid transparent;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.music-option:hover {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4CAF50;
}

.music-option.active {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

.modal-content button {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.modal-content button:hover {
  background: #45a049;
  transform: translateY(-2px);
}

/* Header styles */
.creative-header {
  text-align: center;
  margin-bottom: 2rem;
}

.creative-header h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.highlight {
  color: #4CAF50;
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 2rem;
}

/* Content layout */
.creative-content {
  display: grid;
  gap: 4rem;
}

.creative-section {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.creative-section h2 {
  color: #4CAF50;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.section-intro {
  color: #666;
  margin-bottom: 2rem;
  font-style: italic;
}

/* Bookshelf styles */
.books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.book-item {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(76, 175, 80, 0.05);
  border-radius: 0.5rem;
  transition: transform 0.3s ease;
}

.book-item:hover {
  transform: translateY(-5px);
}

.book-cover {
  flex-shrink: 0;
  width: 80px;
  height: 120px;
  background: #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.book-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.book-info h3 {
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.author {
  color: #4CAF50;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.book-note {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Coffee corner styles */
.coffee-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.coffee-item {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(139, 69, 19, 0.05);
  border-radius: 0.5rem;
  transition: transform 0.3s ease;
}

.coffee-item:hover {
  transform: translateY(-5px);
}

.coffee-image {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  background: #ddd;
  border-radius: 50%;
  overflow: hidden;
}

.coffee-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.coffee-info h3 {
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.location {
  color: #8B4513;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.coffee-note {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Thoughts section styles */
.thoughts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.thought-item {
  padding: 1.5rem;
  background: rgba(76, 175, 80, 0.05);
  border-radius: 0.5rem;
  border-left: 4px solid #4CAF50;
  transition: transform 0.3s ease;
}

.thought-item:hover {
  transform: translateY(-5px);
}

.thought-item h3 {
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.thought-date {
  color: #4CAF50;
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.thought-content {
  color: #666;
  line-height: 1.6;
  font-style: italic;
}

/* Dark theme support */
[data-theme="dark"] .creative-corner {
  color: var(--text-primary, #fff);
}

[data-theme="dark"] .modal-content {
  background: var(--card-bg, #1e1e1e);
  color: var(--text-primary, #fff);
}

[data-theme="dark"] .book-item-mini,
[data-theme="dark"] .coffee-item-mini {
  background: rgba(76, 175, 80, 0.2);
}

[data-theme="dark"] .music-option {
  background: rgba(76, 175, 80, 0.2);
  color: var(--text-primary, #fff);
}

[data-theme="dark"] .scene-background {
  filter: brightness(0.7);
}

/* Responsive design */
@media (max-width: 768px) {
  .creative-corner {
    padding: 1rem;
  }

  .creative-header h1 {
    font-size: 2.5rem;
  }

  .game-scene {
    height: 400px;
  }

  .player {
    font-size: 1.5rem;
  }

  .bookshelf, .coffee-counter, .music-player {
    font-size: 2rem;
  }

  .modal-content {
    margin: 1rem;
    padding: 1.5rem;
    max-width: calc(100% - 2rem);
  }

  .interaction-hint {
    font-size: 0.7rem;
    padding: 0.3rem 0.8rem;
  }
}

@media (max-width: 480px) {
  .game-scene {
    height: 300px;
  }

  .player {
    font-size: 1.2rem;
  }

  .bookshelf, .coffee-counter, .music-player {
    font-size: 1.5rem;
  }

  .modal-content {
    padding: 1rem;
  }
}
