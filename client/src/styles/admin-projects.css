/* client/src/styles/admin-projects.css */
.admin-project-list {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .admin-project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  .admin-project-header h1 {
    font-size: 1.8rem;
    color: #333;
    margin: 0;
  }
  
  .new-project-btn {
    padding: 0.6rem 1.2rem;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
  }
  
  .new-project-btn:hover {
    background-color: #45a049;
  }
  
  .admin-project-filters {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 1.5rem;
  }
  
  .category-filter select {
    padding: 0.6rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    min-width: 180px;
  }
  
  /* 项目网格样式 */
  .admin-project-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
  }
  
  .admin-project-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }
  
  .admin-project-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  }
  
  .project-card-image {
    position: relative;
    height: 180px;
    overflow: hidden;
  }
  
  .project-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .admin-project-card:hover .project-card-image img {
    transform: scale(1.05);
  }
  
  .featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #FFC107;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
  }
  
  .project-card-content {
    padding: 1.5rem;
  }
  
  .project-card-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    color: #333;
  }
  
  .project-card-content p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
    height: 2.8em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  .project-tech-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }
  
  .tech-tag {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
  }
  
  .more-tag {
    color: #999;
    font-size: 0.75rem;
  }
  
  .project-category {
    margin-bottom: 1rem;
  }
  
  .category-badge {
    display: inline-block;
    padding: 0.3rem 0.6rem;
    border-radius: 4px;
    font-size: 0.8rem;
    color: white;
  }
  
  .category-badge.frontend {
    background: #2196F3;
  }
  
  .category-badge.backend {
    background: #9C27B0;
  }
  
  .category-badge.fullstack {
    background: #4CAF50;
  }
  
  .category-badge.research {
    background: #FF9800;
  }
  
  .project-actions {
    display: flex;
    gap: 0.5rem;
  }
  
  .edit-btn, .delete-btn {
    flex: 1;
    padding: 0.4rem 0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
  }
  
  .edit-btn {
    background: #2196F3;
    color: white;
  }
  
  .edit-btn:hover {
    background: #1E88E5;
  }
  
  .delete-btn {
    background: #f44336;
    color: white;
  }
  
  .delete-btn:hover {
    background: #e53935;
  }
  
  /* 项目表单样式 */
  .project-form-container {
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
  }
  
  .project-form {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    padding: 2rem;
  }
  
  .form-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #eee;
  }
  
  .form-section h3 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 1rem;
  }
  
  .images-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
  }
  
  .image-item {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .image-preview {
    height: 100px;
    overflow: hidden;
  }
  
  .image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .image-item button {
    width: 100%;
    padding: 0.4rem;
    border: none;
    background: #f44336;
    color: white;
    cursor: pointer;
    font-size: 0.8rem;
    transition: background-color 0.3s ease;
  }
  
  .image-item button:hover {
    background: #e53935;
  }
  
  /* 加载和错误状态 */
  .loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 0;
  }
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(76, 175, 80, 0.2);
    border-radius: 50%;
    border-top-color: #4CAF50;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 1rem;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  
  .error-message {
    background: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
  }
  
  .no-projects-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    color: #666;
  }
  
  /* 响应式调整 */
  @media (max-width: 768px) {
    .admin-project-grid {
      grid-template-columns: 1fr;
    }
  }
