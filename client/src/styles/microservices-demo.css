.microservices-demo {
    padding: 2rem;
    background: #1a1a1a;
    min-height: 100vh;
    color: #fff;
  }
  
  .demo-controls {
    text-align: center;
    margin-bottom: 2rem;
  }
  
  .demo-controls h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #4CAF50;
  }
  
  .demo-controls p {
    color: #888;
  }
  
  .services-map {
    position: relative;
    width: 100%;
    height: 600px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 16px;
    margin-bottom: 2rem;
    overflow: hidden;
  }
  
  .service-node {
    position: absolute;
    width: 200px;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .service-node:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  .service-node.selected {
    border-color: #4CAF50;
  }
  
  .service-node h3 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .service-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
  }
  
  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4CAF50;
  }
  
  .service-node.healthy .status-indicator {
    background: #4CAF50;
  }
  
  .service-node.degraded .status-indicator {
    background: #FFC107;
  }
  
  .service-node.failed .status-indicator {
    background: #f44336;
  }
  
  .load-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.25rem;
  }
  
  .load-fill {
    height: 100%;
    background: #4CAF50;
    transition: width 0.3s ease;
  }
  
  .load-label {
    font-size: 0.8rem;
    color: #888;
  }
  
  .connections {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
  
  .connection {
    stroke: rgba(255, 255, 255, 0.1);
    stroke-width: 2;
  }
  
  .connection.sending {
    stroke: #4CAF50;
    stroke-dasharray: 5,5;
    animation: dash 1s linear infinite;
  }
  
  .connection.success {
    stroke: rgba(76, 175, 80, 0.3);
  }
  
  .connection.failed {
    stroke: rgba(244, 67, 54, 0.3);
  }
  
  .message {
    fill: #4CAF50;
  }
  
  .message.sending {
    fill: #FFC107;
  }
  
  .message.success {
    fill: #4CAF50;
  }
  
  .message.failed {
    fill: #f44336;
  }
  
  @keyframes dash {
    to {
      stroke-dashoffset: -10;
    }
  }
  
  .metrics-panel {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
  }
  
  .metrics-panel h3 {
    margin-bottom: 1rem;
    color: #4CAF50;
  }
  
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .metric-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
  }
  
  .metric-card h4 {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #fff;
  }
  
  .metric-stats {
    font-size: 0.8rem;
    color: #888;
  }
  
  .metric-stats > div {
    margin-bottom: 0.25rem;
  }
  
  .metric-stats .healthy {
    color: #4CAF50;
  }
  
  .metric-stats .degraded {
    color: #FFC107;
  }
  
  .metric-stats .failed {
    color: #f44336;
  }
  
  /* Service node positions */
  .service-node:nth-child(1) { /* API Gateway */
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
  }
  
  .service-node:nth-child(2) { /* Auth Service */
    top: 40%;
    left: 20%;
  }
  
  .service-node:nth-child(3) { /* User Service */
    top: 40%;
    right: 20%;
  }
  
  .service-node:nth-child(4) { /* Database Service */
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
  }
  
  @media (max-width: 768px) {
    .services-map {
      height: 800px;
    }
  
    .service-node {
      width: 160px;
    }
  
    .metrics-grid {
      grid-template-columns: 1fr;
    }
  }
