.landing-page {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  /* gap: 2rem; */
  padding: 2rem;
  height: 100%;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 80px); /* 减去导航栏高度 */
  padding: 0 4rem;  /* 修改padding，只保留左右间距 */
}

.left-content {
  padding-right: 2rem;
}

.left-content h1 {
  font-size: 2.8rem;
  line-height: 1.2;
  margin-bottom: 1rem;
  font-weight: 700;
}

.highlight {
  color: #4CAF50;
}

.left-content h2 {
  font-size: 1.8rem;
  color: #666;
  margin-bottom: 1.5rem;
}

.left-content p {
  font-size: 1.1rem;
  color: #555;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.action-buttons {
  display: flex;
  gap: 1rem;
}

.primary-btn, .secondary-btn {
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.primary-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
}

.primary-btn:hover {
  background-color: #45a049;
  transform: translateY(-2px);
}

.secondary-btn {
  background-color: transparent;
  color: #4CAF50;
  border: 2px solid #4CAF50;
}

.secondary-btn:hover {
  background-color: rgba(76, 175, 80, 0.1);
  transform: translateY(-2px);
}

.center-content {
  position: relative;
}

.image-container {
  position: relative;
  width: 400px;
  height: 500px;
  z-index: 1;
}

.background-circle {
  position: absolute;
  width: 320px;
  height: 320px;
  background-color: #4CAF50;
  border-radius: 50%;
  opacity: 0.8;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
}

.image-container img,
.image-container picture {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: relative;
  z-index: 2;
  transform: translateY(50px);
  filter:
    contrast(0.9)     /* 增加对比度 */
    saturate(1.1)     /* 增加饱和度 */
    brightness(1.1)
    drop-shadow(0 10px 15px rgba(0,0,0,0.2));
  transition: opacity 0.3s ease, filter 0.3s ease;
}

/* 图片加载状态 */
.image-container img.loading {
  opacity: 0.5;
  filter:
    contrast(0.9)
    saturate(1.1)
    brightness(1.1)
    blur(10px)
    drop-shadow(0 10px 15px rgba(0,0,0,0.2));
}

.image-container img.loaded {
  opacity: 1;
  filter:
    contrast(0.9)
    saturate(1.1)
    brightness(1.1)
    blur(0)
    drop-shadow(0 10px 15px rgba(0,0,0,0.2));
}

.right-content {
  padding-left: 2rem;
}

.stats {
  display: grid;
  gap: 1.5rem;
}

.stat-card {
  background: #fff;
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card h3 {
  font-size: 2.5rem;
  color: #4CAF50;
  margin: 0;
  margin-bottom: 0.5rem;
}

.stat-card p {
  font-size: 1rem;
  color: #666;
  margin: 0;
}

@media (max-width: 1200px) {
  .landing-page {
    grid-template-columns: 1fr auto;
    padding: 2rem;
  }

  .right-content {
    display: none;
  }

  .image-container {
    width: 400px;
    height: 500px;
  }

  .background-circle {
    width: 350px;
    height: 350px;
  }
}

@media (max-width: 768px) {
  .landing-page {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 2rem;
    text-align: center;
    place-items: center;
    padding: 2rem;
  }

  .left-content {
    padding-right: 0;
    order: 2; /* 将文字内容放到第二位 */
  }

  .center-content {
    order: 1; /* 将图片放到第一位 */
    margin: 0 auto;
  }

  /* .image-container {
    width: 300px;
    height: 400px;
    margin: 0 auto;
  } */

  .right-content {
    display: block; /* 显示右侧内容 */
    order: 3; /* 放在最后 */
    padding-left: 0; /* 移除左边距 */
    width: 100%; /* 占满宽度 */
  }

  /* 修改统计卡片在移动端的布局 */
  .stats {
    display: grid;
    grid-template-columns: 1fr 1fr; /* 两列布局 */
    gap: 1rem;
    margin-top: 1rem;
  }

  .stat-card {
    text-align: center;
    padding: 1.2rem;
  }

  .background-circle {
    width: 250px;
    height: 250px;
  }

  .action-buttons {
    justify-content: center;
  }

  /* 调整聊天气泡的位置 */
  .chat-bubble {
    position: static; /* 取消绝对定位 */
    margin-top: 20px; /* 添加上边距 */
    margin-bottom: 20px;
    /* 防止 Framer Motion 的动画影响位置 */
    /* transform: none !important;  */
  }

  .image-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

/* 添加更小屏幕的适配 */
@media (max-width: 480px) {
  .image-container {
    width: 250px;
    height: 350px;
  }

  .background-circle {
    width: 200px;
    height: 200px;
  }

  .left-content h1 {
    font-size: 2.5rem;
  }

  .left-content h2 {
    font-size: 1.5rem;
  }
  .stats {
    gap: 0.8rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-card h3 {
    font-size: 2rem;
  }

  .stat-card p {
    font-size: 0.9rem;
  }
}

.chat-bubble {
  position: absolute;
  top: 0px;
  right: -100px;
  background: #4CAF50;
  padding: 15px 28px;
  border-radius: 25px;
  border: none;
  box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
  cursor: pointer;
  font-size: 17px;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  overflow: hidden;
  z-index: 2;
}

.chat-bubble::before {
  content: '';
  position: absolute;
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  border-style: solid;
  border-width: 12px 12px 12px 0;
  border-color: transparent #4CAF50 transparent transparent;
}

.chat-bubble::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255,255,255,0.1) 0%,
    rgba(255,255,255,0.2) 50%,
    rgba(255,255,255,0.1) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chat-bubble:hover::after {
  opacity: 1;
}

/* 手机端样式调整 */
@media (max-width: 768px) {
  .chat-bubble {
    position: absolute;
    top: 10px;
    right: -20px;
    padding: 10px 20px;
    font-size: 14px; /* 减小字体大小 */
    white-space: nowrap; /* 防止文字换行 */
  }

  /* 调整气泡指向箭头的位置和大小 */
  .chat-bubble::before {
    left: -8px;
    border-width: 8px 8px 8px 0;
  }
}

/* 超小屏幕的额外调整 */
@media (max-width: 480px) {
  .chat-bubble {
    right: -10px;
    padding: 8px 16px;
  }
}
