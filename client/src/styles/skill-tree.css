.skill-tree-container {
  position: relative;
  width: 100%;
  min-height: calc(100vh - 100px); /* 减去标题空间 */
  background: #0a0a0a;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 100px; /* 为标题留出空间 */
}

.services-title {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  text-align: center;
  padding: 2rem;
  background: linear-gradient(to bottom, #0a0a0a 60%, transparent);
  z-index: 10;
}

.services-title h1 {
  font-size: 2.5rem;
  color: #fff;
  margin-bottom: 0.5rem;
}

.services-title p {
  color: #888;
  font-size: 1.1rem;
}

.skill-tree-svg {
  width: 900px;  /* 固定宽度 */
  height: 600px; /* 固定高度 */
  margin: 0 auto;
}

/* 修改节点交互样式 */
.skill-node {
  cursor: pointer;
  transition: all 0.3s ease;
}

.skill-node:hover .node-circle {
  filter: brightness(1.3);
}

.skill-node.active .node-circle {
  animation: pulse 2s infinite;
}

.skill-node.unlockable {
  filter: brightness(1);
}

.skill-node.locked {
  filter: brightness(0.5);
  cursor: not-allowed;
}

/* 详情面板样式优化 */
.skill-details {
  position: fixed;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  width: 320px;
  max-height: 80vh;
  background: rgba(15, 15, 15, 0.95);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

/* 点击动画 */
@keyframes clickEffect {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

.skill-node.clicking {
  animation: clickEffect 0.3s ease;
}

/* 连接线动画 */
.connection-line {
  stroke-dasharray: 10;
  animation: flowLine 1s linear infinite;
}

@keyframes flowLine {
  to {
    stroke-dashoffset: -20;
  }
}

/* 解锁动画 */
@keyframes unlockPulse {
  0% { transform: scale(1); filter: brightness(1); }
  50% { transform: scale(1.2); filter: brightness(1.5); }
  100% { transform: scale(1); filter: brightness(1); }
}

.skill-node.unlocking {
  animation: unlockPulse 0.5s ease;
}

/* 响应式调整 */
@media (max-width: 1400px) {
  .skill-tree-svg {
    width: 800px;
    height: 500px;
  }
}

@media (max-width: 1200px) {
  .skill-tree-svg {
    width: 700px;
    height: 450px;
  }
  
  .skill-details {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .skill-tree-container {
    padding-top: 80px;
  }

  .skill-tree-svg {
    width: 100%;
    height: 400px;
  }

  .skill-details {
    position: fixed;
    bottom: 0;
    right: 0;
    left: 0;
    top: auto;
    transform: none;
    width: 100%;
    max-height: 50vh;
    border-radius: 12px 12px 0 0;
  }
}
