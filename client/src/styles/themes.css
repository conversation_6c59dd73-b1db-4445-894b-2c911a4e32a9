/* 全局主题变量 */
:root {
  /* 颜色系统 */
  --primary-color: #4CAF50;
  --primary-hover: #45a049;
  --primary-light: rgba(76, 175, 80, 0.1);
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.15);
  
  /* 边框圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;
}

/* 浅色主题 */
.light-theme,
[data-theme="light"] {
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-overlay: rgba(255, 255, 255, 0.9);
  
  /* 文字颜色 */
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-tertiary: #adb5bd;
  --text-inverse: #ffffff;
  
  /* 边框颜色 */
  --border-primary: #dee2e6;
  --border-secondary: #e9ecef;
  --border-focus: var(--primary-color);
  
  /* 卡片样式 */
  --card-bg: #ffffff;
  --card-border: var(--border-primary);
  --card-shadow: var(--shadow-md);
  
  /* 按钮样式 */
  --btn-primary-bg: var(--primary-color);
  --btn-primary-hover: var(--primary-hover);
  --btn-secondary-bg: transparent;
  --btn-secondary-border: var(--primary-color);
  --btn-secondary-hover: var(--primary-light);
  
  /* 输入框样式 */
  --input-bg: #ffffff;
  --input-border: var(--border-primary);
  --input-focus: var(--primary-color);
  
  /* 导航样式 */
  --nav-bg: rgba(255, 255, 255, 0.95);
  --nav-border: rgba(0, 0, 0, 0.1);
  --nav-text: var(--text-primary);
  --nav-hover: var(--primary-light);
  
  /* 聊天气泡 */
  --chat-bubble-bg: var(--primary-color);
  --chat-bubble-text: #ffffff;
  --chat-bubble-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
}

/* 深色主题 */
.dark-theme,
[data-theme="dark"] {
  /* 背景色 */
  --bg-primary: #121212;
  --bg-secondary: #1e1e1e;
  --bg-tertiary: #2a2a2a;
  --bg-overlay: rgba(18, 18, 18, 0.9);
  
  /* 文字颜色 */
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-tertiary: #666666;
  --text-inverse: #121212;
  
  /* 边框颜色 */
  --border-primary: #333333;
  --border-secondary: #2a2a2a;
  --border-focus: var(--primary-color);
  
  /* 卡片样式 */
  --card-bg: #1e1e1e;
  --card-border: var(--border-primary);
  --card-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  
  /* 按钮样式 */
  --btn-primary-bg: var(--primary-color);
  --btn-primary-hover: var(--primary-hover);
  --btn-secondary-bg: transparent;
  --btn-secondary-border: var(--primary-color);
  --btn-secondary-hover: rgba(76, 175, 80, 0.2);
  
  /* 输入框样式 */
  --input-bg: #2a2a2a;
  --input-border: var(--border-primary);
  --input-focus: var(--primary-color);
  
  /* 导航样式 */
  --nav-bg: rgba(30, 30, 30, 0.95);
  --nav-border: rgba(255, 255, 255, 0.1);
  --nav-text: var(--text-primary);
  --nav-hover: rgba(76, 175, 80, 0.2);
  
  /* 聊天气泡 */
  --chat-bubble-bg: var(--primary-color);
  --chat-bubble-text: #ffffff;
  --chat-bubble-shadow: 0 8px 20px rgba(76, 175, 80, 0.4);
}

/* 全局样式应用 */
* {
  transition: background-color var(--transition-normal),
              color var(--transition-normal),
              border-color var(--transition-normal),
              box-shadow var(--transition-normal);
}

body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* Firefox 滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--border-primary) var(--bg-secondary);
}

/* 选择文本样式 */
::selection {
  background-color: var(--primary-light);
  color: var(--text-primary);
}

::-moz-selection {
  background-color: var(--primary-light);
  color: var(--text-primary);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --border-primary: #000000;
    --text-secondary: var(--text-primary);
  }
  
  [data-theme="dark"] {
    --border-primary: #ffffff;
    --text-secondary: var(--text-primary);
  }
}

/* 打印样式 */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
}
