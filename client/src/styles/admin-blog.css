/* client/src/styles/admin-blog.css */
/* 博客列表管理样式 */
.admin-blog-list {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .admin-blog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  .admin-blog-header h1 {
    font-size: 1.8rem;
    color: #333;
    margin: 0;
  }
  
  .new-post-btn {
    padding: 0.6rem 1.2rem;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
  }
  
  .new-post-btn:hover {
    background-color: #45a049;
  }
  
  .admin-blog-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .search-box {
    flex: 1;
  }
  
  .search-box input {
    width: 100%;
    padding: 0.6rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
  }
  
  .category-filter select {
    padding: 0.6rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    min-width: 180px;
  }
  
  /* 表格样式 */
  .admin-blog-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
  
  .admin-blog-table-header {
    display: grid;
    grid-template-columns: 3fr 1fr 2fr 1fr 150px;
    background: #f5f5f5;
    padding: 0.8rem 1rem;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #ddd;
  }
  
  .admin-blog-table-body {
    max-height: 600px;
    overflow-y: auto;
  }
  
  .admin-blog-table-row {
    display: grid;
    grid-template-columns: 3fr 1fr 2fr 1fr 150px;
    padding: 0.8rem 1rem;
    border-bottom: 1px solid #eee;
    align-items: center;
  }
  
  .table-cell {
    padding: 0.25rem;
  }
  
  .title-cell {
    display: flex;
    flex-direction: column;
  }
  
  .post-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
  }
  
  .post-excerpt {
    font-size: 0.85rem;
    color: #666;
  }
  
  .categories-cell {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .category-badge {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
  }
  
  .featured-badge {
    background: #FFC107;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
  }
  
  .not-featured {
    color: #aaa;
  }
  
  .actions-cell {
    display: flex;
    gap: 0.5rem;
  }
  
  .edit-btn, .delete-btn {
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
  }
  
  .edit-btn {
    background: #2196F3;
    color: white;
  }
  
  .edit-btn:hover {
    background: #1E88E5;
  }
  
  .delete-btn {
    background: #f44336;
    color: white;
  }
  
  .delete-btn:hover {
    background: #e53935;
  }
  
  /* 加载和错误状态 */
  .loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
  }
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(76, 175, 80, 0.2);
    border-radius: 50%;
    border-top-color: #4CAF50;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 1rem;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  
  .error-message {
    background: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
  }
  
  .no-posts-message {
    text-align: center;
    padding: 2rem;
    color: #666;
  }
  
  /* 博客表单样式 */
  .blog-post-form-container {
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
  }
  
  .blog-post-form {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    padding: 2rem;
  }
  
  .form-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
  }
  
  .form-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
  }
  
  .form-error {
    background: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
  }
  
  .form-group {
    margin-bottom: 1.5rem;
  }
  
  .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .form-group {
    flex: 1;
  }
  
  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
  }
  
  .form-group input[type="text"],
  .form-group input[type="date"],
  .form-group input[type="number"],
  .form-group textarea,
  .form-group select {
    width: 100%;
    padding: 0.6rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
  }
  
  .form-group input:focus,
  .form-group textarea:focus,
  .form-group select:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
  }
  
  .checkbox-label input {
    width: auto;
  }
  
  .tag-input-container {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }
  
  .tag-input-container input {
    flex: 1;
  }
  
  .tag-input-container button {
    padding: 0.6rem 1rem;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
  }
  
  .tag-input-container button:hover:not(:disabled) {
    background: #45a049;
  }
  
  .tag-input-container button:disabled {
    background: #a5d6a7;
    cursor: not-allowed;
  }
  
  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
  }
  
  .tag {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
  }
  
  .category-tag {
    background: rgba(33, 150, 243, 0.1);
    color: #2196F3;
  }
  
  .tag button {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    font-size: 1rem;
    line-height: 1;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #eee;
  }
  
  .cancel-button,
  .submit-button {
    padding: 0.6rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }
  
  .cancel-button {
    background: #f5f5f5;
    color: #333;
  }
  
  .cancel-button:hover:not(:disabled) {
    background: #e0e0e0;
  }
  
  .submit-button {
    background: #4CAF50;
    color: white;
  }
  
  .submit-button:hover:not(:disabled) {
    background: #45a049;
  }
  
  .submit-button:disabled,
  .cancel-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
  
  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
  }
  
  /* 响应式调整 */
  @media (max-width: 768px) {
    .admin-blog-table-header,
    .admin-blog-table-row {
      grid-template-columns: 2fr 1fr 1fr;
    }
    
    .admin-blog-table-header .table-cell:nth-child(3),
    .admin-blog-table-header .table-cell:nth-child(4),
    .admin-blog-table-row .table-cell:nth-child(3),
    .admin-blog-table-row .table-cell:nth-child(4) {
      display: none;
    }
    
    .form-row {
      flex-direction: column;
      gap: 1.5rem;
    }
  }
