.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  position: relative;
  overflow-x: hidden;
}

.content {
  flex: 1;
  padding-top: 30px;
}

/* 导航容器样式 */
.nav-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
  background: #fff;
  box-shadow: none;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

/* 导航栏基础样式 */
.nav-header {
  position: relative;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: transparent;
  padding: 0 40px;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* Logo样式 */
.logo {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  cursor: pointer;
  position: relative;
  z-index: 2;
  transition: color 0.3s ease;
}

.logo:hover {
  color: #4CAF50;
}

/* 导航菜单样式 */
.nav-menu {
  display: flex;
  gap: 40px;
  align-items: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

/* 导航项样式 */
.nav-item {
  border: none;
  background: none;
  font-size: 16px;
  color: #333;
  cursor: pointer;
  padding: 8px 0;
  position: relative;
  transition: color 0.3s ease;
}

.nav-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: #4CAF50;
  transition: width 0.3s ease;
}

.nav-item:hover {
  color: #4CAF50;
}

.nav-item:hover::after {
  width: 100%;
}

/* 活跃导航项样式 */
.nav-item.active {
  color: #4CAF50;
}

.nav-item.active::after {
  width: 100%;
}

/* 汉堡菜单按钮样式 */
.menu-toggle {
  display: none;
  width: 40px;
  height: 40px;
  padding: 8px;
  border: none;
  background: transparent;
  cursor: pointer;
  position: relative;
  z-index: 2;
}

/* 汉堡菜单线条 */
.menu-line {
  width: 24px;
  height: 2px;
  background-color: #333;
  transition: all 0.3s ease;
  position: relative;
}

.menu-toggle .menu-line + .menu-line {
  margin-top: 6px;
}

/* 汉堡菜单打开状态动画 */
.menu-toggle.open .menu-line:nth-child(1) {
  transform: translateY(8px) rotate(45deg);
}

.menu-toggle.open .menu-line:nth-child(2) {
  opacity: 0;
}

.menu-toggle.open .menu-line:nth-child(3) {
  transform: translateY(-8px) rotate(-45deg);
}

/* 汉堡菜单悬停效果 */
.menu-toggle:hover .menu-line {
  background-color: #4CAF50;
}

/* 移动端下拉菜单 */
.mobile-menu {
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  background: white;
  padding: 20px;
  border-top: 1px solid rgba(0,0,0,0.05);
  box-shadow: 0 4px 8px rgba(0,0,0,0.05);
  animation: slideDown 0.3s ease;
  z-index: 1;
  width: 100%;
  box-sizing: border-box;
  max-width: 1400px;
  margin: 0 auto;
}

.mobile-menu .nav-item {
  width: 100%;
  text-align: left;
  padding: 12px 20px;
  transition: all 0.3s ease;
}

.mobile-menu .nav-item:hover {
  background: rgba(76, 175, 80, 0.1);
  padding-left: 25px;
}

/* 下拉菜单动画 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 聊天相关样式 */
.chat-container {
  position: relative;
  height: 100%;
}

.close-chat {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 32px;
  height: 32px;
  border-radius: 16px;
  border: none;
  background: #f0f2f5;
  color: #666;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 100;
}

.close-chat:hover {
  background: #e4e6eb;
  color: #333;
  transform: rotate(90deg);
}

/* Coming Soon 页面样式 */
.coming-soon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 2rem;
  color: #4CAF50;
}

/* 响应式布局 */
/* 大屏幕 (>=1200px) */
@media (min-width: 1200px) {
  .nav-header {
    padding: 0 80px;
  }
}

/* 平板和小屏幕 (768px - 1199px) */
@media (max-width: 1199px) {
  .nav-header {
    padding: 0 40px;
  }

  .nav-menu {
    gap: 40px;
  }
}

/* 手机屏幕 (<768px) */
@media (max-width: 767px) {
  .nav-header {
    padding: 0 20px;
  }

  .nav-menu {
    display: none;
  }

  .menu-toggle {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .mobile-menu {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .mobile-menu .nav-item::after {
    display: none;
  }
}

/* 超小屏幕 (<480px) */
@media (max-width: 479px) {
  .nav-header {
    padding: 0 15px;
  }

  .logo {
    font-size: 20px;
  }

  .mobile-menu {
    padding: 15px;
  }
}
