.cyberpunk-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #090419 0%, #1B1235 100%);
    overflow: hidden;
    z-index: -1;
  }
  
  /* 网格背景 */
  .grid-overlay {
    position: absolute;
    width: 200%;
    height: 200%;
    background-image: 
      linear-gradient(rgba(64, 224, 208, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(64, 224, 208, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    transform: perspective(500px) rotateX(60deg);
    animation: gridMove 20s linear infinite;
    top: -50%;
  }
  
  /* 光束效果 */
  .light-effects {
    position: absolute;
    width: 100%;
    height: 100%;
  }
  
  .light-beam {
    position: absolute;
    width: 2px;
    height: 100%;
    background: linear-gradient(
      to bottom,
      transparent,
      rgba(64, 224, 208, 0.3),
      rgba(64, 224, 208, 0.5),
      rgba(64, 224, 208, 0.3),
      transparent
    );
    filter: blur(3px);
    animation: beamMove 8s infinite;
  }
  
  .beam-1 { left: 20%; animation-delay: 0s; }
  .beam-2 { left: 40%; animation-delay: 2s; }
  .beam-3 { left: 60%; animation-delay: 4s; }
  .beam-4 { left: 80%; animation-delay: 6s; }
  .beam-5 { left: 90%; animation-delay: 8s; }
  
  /* 故障效果 */
  .glitch-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    mix-blend-mode: overlay;
    animation: glitchEffect 8s steps(1) infinite;
    opacity: 0.05;
    background-image: repeating-linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px,
      transparent 2px
    );
  }
  
  /* 扫描线效果 */
  .scan-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      to bottom,
      transparent 50%,
      rgba(64, 224, 208, 0.02) 50%
    );
    background-size: 100% 4px;
    animation: scanMove 10s linear infinite;
  }
  
  /* 装饰图案 */
  .cyber-patterns {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
  
  .corner-decoration {
    animation: glowPulse 4s ease-in-out infinite;
  }
  
  /* 动画定义 */
  @keyframes gridMove {
    0% {
      transform: perspective(500px) rotateX(60deg) translateY(0);
    }
    100% {
      transform: perspective(500px) rotateX(60deg) translateY(50px);
    }
  }
  
  @keyframes beamMove {
    0% {
      transform: translateY(-100%) rotate(15deg);
      opacity: 0;
    }
    20% {
      opacity: 0.8;
    }
    80% {
      opacity: 0.8;
    }
    100% {
      transform: translateY(100%) rotate(15deg);
      opacity: 0;
    }
  }
  
  @keyframes glitchEffect {
    0% { opacity: 0.05; }
    1% { opacity: 0.1; }
    2% { opacity: 0.05; }
    50% { opacity: 0.05; }
    51% { opacity: 0.1; }
    52% { opacity: 0.05; }
    90% { opacity: 0.05; }
    91% { opacity: 0.1; }
    92% { opacity: 0.05; }
    100% { opacity: 0.05; }
  }
  
  @keyframes scanMove {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(100%);
    }
  }
  
  @keyframes glowPulse {
    0% { opacity: 0.2; }
    50% { opacity: 0.4; }
    100% { opacity: 0.2; }
  }
  
  /* 全局阴影效果 */
  .cyberpunk-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
      circle at 50% 50%,
      rgba(64, 224, 208, 0.1) 0%,
      transparent 60%
    );
    pointer-events: none;
  }
