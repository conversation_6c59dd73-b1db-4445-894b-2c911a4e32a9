/* client/src/styles/loading.css */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  font-size: 1.2rem;
  color: #4CAF50;
  position: relative;
}

.loading-container::after {
  content: '';
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(76, 175, 80, 0.2);
  border-top-color: #4CAF50;
  animation: spin 1s linear infinite;
  margin-top: 60px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
