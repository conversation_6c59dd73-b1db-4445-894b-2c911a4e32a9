// client/src/services/api.ts - 添加删除博客文章功能

import axios from "axios";

const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:3001";

// 博客API
export const getBlogPosts = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/blog/posts`);
    return response.data;
  } catch (error) {
    console.error("Error fetching blog posts:", error);
    throw error;
  }
};

export const getBlogPost = async (id: string) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/blog/posts/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching blog post:", error);
    throw error;
  }
};

export const createBlogPost = async (postData: any) => {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/api/blog/posts`,
      postData
    );
    return response.data;
  } catch (error) {
    console.error("Error creating blog post:", error);
    throw error;
  }
};

export const updateBlogPost = async (id: string, postData: any) => {
  try {
    const response = await axios.put(
      `${API_BASE_URL}/api/blog/posts/${id}`,
      postData
    );
    return response.data;
  } catch (error) {
    console.error("Error updating blog post:", error);
    throw error;
  }
};

export const deletePost = async (id: string) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/api/blog/posts/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting blog post:", error);
    throw error;
  }
};

export const getBlogCollections = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/blog/collections`);
    return response.data;
  } catch (error) {
    console.error("Error fetching blog collections:", error);
    throw error;
  }
};

export const getBlogCollection = async (id: string) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/blog/collections/${id}`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching blog collection:", error);
    throw error;
  }
};

// 项目API
export const getProjects = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/projects`);
    return response.data;
  } catch (error) {
    console.error("Error fetching projects:", error);
    throw error;
  }
};

export const getProject = async (id: number) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/projects/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching project:", error);
    throw error;
  }
};

export const createProject = async (projectData: any) => {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/api/projects`,
      projectData
    );
    return response.data;
  } catch (error) {
    console.error("Error creating project:", error);
    throw error;
  }
};

export const updateProject = async (id: number, projectData: any) => {
  try {
    const response = await axios.put(
      `${API_BASE_URL}/api/projects/${id}`,
      projectData
    );
    return response.data;
  } catch (error) {
    console.error("Error updating project:", error);
    throw error;
  }
};

export const deleteProject = async (id: number) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/api/projects/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting project:", error);
    throw error;
  }
};

// 联系表单API
export const submitContactForm = async (formData: any) => {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/api/form/submit`,
      formData
    );
    return response.data;
  } catch (error) {
    console.error("Error submitting form:", error);
    throw error;
  }
};

// 聊天API
export const sendMessage = async (content: string) => {
  try {
    // 设置超时时间为15秒
    const response = await axios.post(
      `${API_BASE_URL}/api/chat`,
      {
        message: content,
      },
      {
        timeout: 20000, // 20秒超时
      }
    );

    if (!response.data?.choices?.[0]?.message?.content) {
      throw new Error("Invalid response format");
    }

    return response.data.choices[0].message.content;
  } catch (error) {
    console.error("Error sending message:", error);
    if (axios.isAxiosError(error)) {
      if (error.code === "ECONNABORTED" || error.message.includes("timeout")) {
        throw new Error(
          "Request timed out. The AI is taking too long to respond. Please try again."
        );
      }
      if (error.response?.status === 429) {
        throw new Error("Too many requests. Please try again later.");
      }
      if (error.response?.status === 504) {
        throw new Error(
          "The AI is taking too long to respond. Please try again."
        );
      }
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
    }
    throw new Error("Failed to send message. Please try again.");
  }
};
// // client/src/services/api.ts
// export const sendMessage = async (content: string) => {
//   try {
//     const response = await fetch('http://localhost:3001/api/chat', {
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/json'
//       },
//       body: JSON.stringify({
//         message: content  // 修改为符合新后端 API 的格式
//       })
//     });

//     if (!response.ok) {
//       throw new Error('Network response was not ok');
//     }

//     const data = await response.json();
//     return data.choices[0].message.content;
//   } catch (error) {
//     console.error('Error:', error);
//     throw error;
//   }
// };

// const API_URL = 'https://api.openai.com/v1/chat/completions'; // 后面我们会设置具体的URL
// const API_KEY = import.meta.env.VITE_OPENAI_API_KEY || '';
// const SYSTEM_PROMPT = `

// 你是 Taylor Zhu 的个人介绍助手，以专业友好的方式只用英文与访客交流。以下是个人信息：

// 专业技能：
// - 全栈开发：精通 Elasticsearch、AngularJS、微服务架构
// - 性能优化：擅长页面加载优化、缓存策略、数据库查询优化
// - 前端开发：React组件开发，响应式设计
// - 后端开发：API开发与优化，复杂数据处理
// - 语言能力：英语、粤语、中文（均为母语或双语水平）

// 教育背景：
// - 圣克拉拉大学 - 计算机科学博士 (2023-2024)
// - 圣克拉拉大学 - 计算机科学硕士 (2021-2023)
// - 华南理工大学 - 计算机科学学士 (2017-2021)
// - 卡内基梅隆大学 - 数据科学证书 (2020)

// 工作经验：
// - 圣克拉拉大学 - 博士研究助理 (2023.09-2024.06)
// - KellynKai Chinese - 软件工程实习生 (2022.08-2023.01)
// - 圣克拉拉大学 - 研究生研究助理 (2021.10-2022.06)
// - 华为 - 软件工程实习生 (2020.07-2021.06)
// - 腾讯 - 软件工程实习生 (2020.01-2020.06)

// 获奖经历：
// - 数学建模竞赛
// - Solaris AI GPT-4o vs Gemini Hackathon 第一名
// - Volunteer AWE USA 2023

// 个人特点：
// - 热衷于构建高性能、稳健、高效的软件解决方案
// - 注重代码优雅性和系统设计的美感
// - 全栈开发和性能优化领域的专业经验
// - 喜欢在海边冲浪、徒步探索新的小径、寻找完美的咖啡

// Answer Rule：
// 1. 用友好专业的语气回答
// 2. 根据问题重点展示相关经验和技能
// 3. 强调全栈开发和性能优化的专业背景
// 4. 适当展现对技术的热情和对艺术设计的兴趣
// 5. 如遇不了解的信息，诚实表示不知道
// 6. 回答尽可能精炼

// # Taylor Zhu 个人介绍助手 - 详细版本

// ## 专业概况

// ### 技术技能
// - 全栈开发：精通多语言（Java, Python, JavaScript）和微服务架构
// - 性能优化：擅长页面加载优化、缓存策略、数据库查询性能提升
// - 前端开发：React/Vue组件开发，响应式设计
// - 后端开发：分布式系统设计，复杂数据处理
// - 研究领域: 子模算法、机器学习算法

// 教育背景
// - 圣克拉拉大学 - 计算机科学博士 (2023-2024)
// - 圣克拉拉大学 - 计算机科学硕士 (2021-2023)
// - 华南理工大学 - 计算机科学学士 (2017-2021)

// 职业亮点与领导力经历:

// - 华为实习 (2020.07-2021.06):
//   (项目：微服务采购可视化平台)
//   - 团队技术负责人，协调5人开发团队
//   - 制定技术roadmap和开发规范
//   - 将项目交付周期缩短30%
//   - 通过code review和技术分享提升团队技术水平

// ### KellynKai实习 (2022.08-2023.01)
//   项目：前端性能优化
//   - 识别并解决前端性能瓶颈
//   - 页面加载时间从4s降至1.5s
//   - 与产品经理和设计团队紧密沟通

// ### 圣克拉拉大学研究项目
// 智能电网故障传播模型
//   - 设计创新采样算法
//   - 将网络节点故障传播范围从350个限制到200个
//   - 提升系统韧性和安全性

// 典型行为问题(BQ)参考回答:

// 克服挑战的经历:
//   在智能电网故障传播模型研究中：
//   - 深入文献研究
//   - 设计创新的采样算法
//   - 与指导教授多次讨论和迭代
//   成功将网络节点故障传播范围显著缩小

// - 领导团队成功案例:
//   在华为分布式环境监测平台开发中：
//   - 制定团队开发规范
//   - 分配任务并追踪进度
//   - 解决团队技术分歧
//   按期高质量交付项目，获得团队和主管好评

// - 处理技术冲突:
//   在前端优化项目中:
//   - 冷静倾听对方观点
//   - 提供数据和性能测试支持方案
//   - 通过技术对比，团队最终采纳优化方案

// - 持续学习与成长：
//   - 积极参加技术研讨会
//   - 关注行业前沿技术
//   - 每月至少完成一个技术专项学习
//   - Solaris AI黑客马拉松获得第一名

// 个人特质:

// 1. 专业特点:
// - 技术驱动型人才
// - 对解决复杂问题充满热情
// - 善于将技术难题转化为创新解决方案

// 2. 个人爱好:
// - 海边冲浪:
//   - 在Linda Mar， Ocean Beach等海滩冲浪
// - 徒步探险:
// - 品尝精品咖啡:
//   - 最喜欢旧金山的精品咖啡，特别是Third wave coffee的代表Four Barrel等咖啡店
//   - 喜欢品尝酸度较高的咖啡

// 3. 语言能力:
// - 中文（母语）
// - 英语（母语水平）
// - 粤语（母语水平）

// 4. 交流风格:
// 1. 专业且富有感染力
// 2. 善于将技术解释得生动易懂
// 3. 展现对技术的热爱和职业追求
// 4. 保持开放和谦逊的态度

// 5. 职业荣誉:
// - Solaris AI GPT-4o vs Gemini Hackathon 第一名
// - 数学建模竞赛获奖
// - Volunteer AWE USA 2023

// 6. 职业展望:
// 致力于推动技术创新，用工程解决方案改善人们的生活，成为技术与人文结合的跨界创新者。

// 个人信息：
// - 性别、称呼：男生（his/him）
// - 年龄：20出头
// - 种族：two or more race

// 外表与形象：
// - 阳光帅气，身高约180cm
// - 运动健康的体型，肩宽腰窄，充满活力
// - 穿衣显瘦，气质儒雅但不失阳光
// - 标准的运动型男生外表，给人精力充沛的感觉

// 个性特征：
// - 性格开朗、乐于沟通
// - 做事认真但不失幽默感
// - 对朋友真诚热情
// - 有很强的同理心和社交能力
// - 喜欢挑战，但不盲目
// - 对生活充满好奇和热情

// 社交风格：
// - 善于倾听
// - 具有感染力的交流方式
// - 能快速建立良好的人际关系
// - 不拘泥于条条框框，思维开放
// - 尊重差异，包容不同观点
// `;

// if (!API_KEY) {
//     throw new Error('Missing OpenAI API key');
//   }

// export async function sendMessage(content: string) {
//   try {
//     console.log('开始API调用，使用的内容:', content);

//     const response = await fetch(API_URL, {
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/json',
//         'Authorization': `Bearer ${API_KEY}`
//       },
//       body: JSON.stringify({
//         model: "gpt-3.5-turbo", // 添加这行，指定模型
//         messages: [
//           {
//             role: "system",
//             content: SYSTEM_PROMPT
//           },
//           {
//             role: "user",
//             content: content
//           }
//         ]
//       })
//     });

//     if (response.status === 429) {
//         return 'Sorry，API calls ran out, please email me: <EMAIL>';
//       }

//       if (!response.ok) {
//         return 'Sorry, something went wrong, please try again!';
//       }

//       const data = await response.json();
//       return data.choices[0].message.content;
//     } catch (error) {
//       console.error('API调用错误:', error);
//       return 'Sorry, the network has some issues, please check you connectionand try again!';
//     }
// }
