services:
  - type: web
    name: taylorzhu-portfolio-api
    env: node
    region: oregon # 可以根据需要更改区域
    buildCommand: cd server && npm install && npm run build
    startCommand: cd server && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3001
      - key: MONGODB_URI
        sync: false # 这表示这是一个需要在 Render 仪表板中手动设置的敏感变量
      - key: JWT_SECRET
        sync: false
      - key: CLIENT_URL
        value: https://taylorzhu-portfolio.vercel.app # 替换为你的 Vercel 前端 URL
    healthCheckPath: /health
    autoDeploy: true # 设置为 false 如果你不想自动部署
